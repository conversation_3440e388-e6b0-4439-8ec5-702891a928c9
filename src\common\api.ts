import axios, { AxiosResponse } from 'axios';
import { useAuthStore } from '../store';
import { differenceInMinutes } from 'date-fns';

declare global {}

export type ApiResponse<T> = AxiosResponse<T>;

const env = (import.meta as any).env;

export const api = axios.create({
    baseURL: env.VITE_API_URL || 'https://api.spsstaging.com/api',
});

export const publicApi = axios.create({
    baseURL: env.VITE_API_URL || 'https://api.spsstaging.com/api',
});

// Request interceptor for API calls
api.interceptors.request.use(
    async (config) => {
        const expiresAt = useAuthStore.getState().expiresAt;
        config.headers['Accept'] = 'application/json';
        config.headers['Content-Type'] = 'application/json';
        const minuteDiff = expiresAt ? differenceInMinutes(new Date(), expiresAt) : -1;

        if (expiresAt && minuteDiff <= 60 && minuteDiff >= 0) {
            await useAuthStore.getState().refresh();
        }

        if (useAuthStore.getState().accessToken) {
            config.headers['Authorization'] = `Bearer ${useAuthStore.getState().accessToken}`;
        }

        return config;
    },
    (error) => {
        Promise.reject(error);
    },
);

// Response interceptor for API calls
api.interceptors.response.use(
    (response) => {
        return response;
    },
    async function (error) {
        // check if error reason is wrong access token. Status 401 expected only in case of invalid access token
        if (error.response && error.response.status === 401) {
            // clear user data
            useAuthStore.getState().unload();
        }
        return Promise.reject(error);
    },
);
