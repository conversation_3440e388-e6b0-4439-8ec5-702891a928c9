import React from 'react';

interface Props {
    className?: string;
    variation?: 'horizontal' | 'vertical';
}

const Degrees = {
    horizontal: 0,
    vertical: 90,
};

const Dots: React.FC<Props> = ({ className, variation = 'horizontal' }) => {
    const deg = Degrees[variation];
    
    return (
        <svg className={className}
             style={{
                 transform: `rotate(${deg}deg)`,
             }}
             width="1em"
             height="1em"
             xmlns="http://www.w3.org/2000/svg"
             fill="none"
             viewBox="0 0 24 24"
        >
            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                  d="M12 12v.001M16 12v.001M8 12v.001"/>
        </svg>
    );
};

export default Dots;
