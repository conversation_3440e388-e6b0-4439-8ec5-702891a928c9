import React from 'react';

interface Props {
    className?: string;
    direction?: 'left' | 'right' | 'up' | 'down';
}

const Degrees = {
    left: 90,
    right: 270,
    up: 180,
    down: 0,
};

const Chevron: React.FC<Props> = ({ className, direction = 'right' }) => {
    const deg = Degrees[direction];
    
    return (
        <svg
            className={className}
            style={{
                transform: `rotate(${deg}deg)`,
            }}
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M17.5 9.5 12 15 6.5 9.5"
            />
        </svg>
    );
};

export default Chevron;
