import React from 'react';
import { Form, Input } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import classNames from 'classnames';

interface Props {
    className?: string;
    label?: string;
    fontSize?: 'default' | 'medium';
    name: string;
    rules?: any[];
    placeholder?: string;
    fieldSize?: 'default' | 'medium';
}

const PasswordField: React.FC<Props> = ({ className, fontSize, fieldSize, label, name, rules, placeholder }) => {
    
    return (
        <Form.Item
            className={classNames("form-field", fontSize, className)}
            name={name}
            label={label}
            rules={rules}
        >
            <Input.Password
                className={classNames(fieldSize)}
                rootClassName="form-field-input"
                placeholder={placeholder}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
        </Form.Item>
    );
};

export default PasswordField;
