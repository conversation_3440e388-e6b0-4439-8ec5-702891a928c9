.modal {
    height: 100vh;
    opacity: 0;
    position: fixed;
    left: 0;
    top: 0;
    transition: all 0.3s ease;
    visibility: hidden;
    width: 100vw;
    z-index: 11;
    display: flex;
    align-items: center;
    justify-content: center;

    &.show {
        opacity: 1;
        visibility: visible;
        .modal-content,
        .modal-close {
            transform: translate(0);
        }
    }

    &-content {
        background-color: $color-background-light;
        border-radius: $radius-lg;
        box-shadow: 0 8px 32px 0 rgba(16,38,100,0.12);
        max-width: 540px;
        width: 100%;
        position: relative;
        overflow: hidden;
        border: 1px solid $color-border;
        display: flex;
        flex-direction: column;
        padding: 0;
        margin: 0 16px;
        transform: none;
        height: auto;
        z-index: 20;
        @media (max-width: 600px) {
            max-width: 98vw;
        }

        &.medium {
            max-width: 600px;
        }

        &.big {
            max-width: 800px;
        }

        &.large {
            max-width: 1200px;
            width: 90vw;
            height: 90vh;
        }
        &-inner {
            @include flex(flex, column, nowrap, flex-start, stretch);
            flex: 1;
            height: 100%;
            overflow: auto;
            padding: $spacing-lg $spacing-lg;
        }
        &-head {
            @include flex(flex, column, nowrap, flex-start, flex-start);
            gap: $spacing-lg;
            margin-bottom: $spacing-lg;
            &-title {
                margin-bottom: $spacing-xs;
                font-size: $font-size-lg;
                font-weight: $font-weight-bold;
            }
            &-description {
                margin-bottom: 0;
                color: $color-text-light;
            }
            &-close {
                margin-left: auto;
                width: auto !important;
            }
        }
    }
    &-overlay {
        bottom: 0;
        left: 0;
        right: 0;
        position: fixed;
        top: 0;
        background: rgba(16,38,100,0.08);
        z-index: 10;
    }
}
