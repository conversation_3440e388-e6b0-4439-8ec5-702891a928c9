.breadcrumb {

    ol {
        @include flex(flex, row, nowrap, flex-start, center);

        li {
            line-height: 1;
        }
    }

    .ant-breadcrumb {

        &-link {
            @include flex(flex, row, nowrap, flex-start, center);
            @include link-color($color-primary, $color-primary, none, none);
            cursor: pointer;
            font-weight: $font-weight-bold;
            gap: 12px;
            height: initial;

            &:hover {
                background-color: initial;
            }

            span {
                color: $color-primary;
            }
        }

        &-separator {
            color: $color-primary;
            margin-inline: 6px;
        }
    }
}
