.accordion {
    background-color: transparent;
    border: none;
    border-radius: 0;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.02), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.03);

    &-item {
        border: 1px solid #E1E2E4 !important;
        border-radius: 0 !important;
        margin-bottom: 16px;
    }

    &-header {
        align-items: center !important;
        background-color: $color-background-light !important;
        padding: 16px 24px !important;

        &-text {
            font-size: 18px;
            font-weight: $font-weight-bold;
        }
    }

    &-content {
        border-top: none !important;

        &-box {
            padding: 8px 64px 24px 24px !important;
        }

        &-meta {

            &-label {
                color: $gray-500;
                font-weight: $font-weight-bold;
                margin-bottom: 4px;
            }
        }
    }

    &-arrow {
        font-size: 24px !important;
    }

    &.secondary {

        .accordion {

            &-item {
                border-radius: 0 !important;
                margin-bottom: 16px;
            }

            &-header {
                align-items: center !important;
                background-color: transparent;
                padding: 16px !important;

                &-text {
                    font-size: 16px;
                    font-weight: $font-weight-bold;
                }
            }

            &-content {
                border-top: none !important;

                &-box {
                    padding: 0 16px 16px !important;
                }
            }
        }
    }
}
