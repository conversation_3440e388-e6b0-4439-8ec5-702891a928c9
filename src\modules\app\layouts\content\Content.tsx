import React from 'react';
import Header from '../header/Header.tsx';
import classNames from 'classnames';
import { BreadcrumbItemType } from 'antd/lib/breadcrumb/Breadcrumb';

interface Props {
    breadcrumbItems?: BreadcrumbItemType[];
    children: React.ReactNode;
    className?: string;
    classNameInner?: string;
    headerActions?: React.ReactNode;
    headerSubtitle?: string;
    headerTitle?: React.ReactNode;
}

const Content: React.FC<Props> = ({
    breadcrumbItems,
    children,
    className,
    classNameInner,
    headerActions,
    headerSubtitle,
    headerTitle,
}) => {
    return (
        <div
            className={classNames("main-content-body", className)}
            style={{
                padding: '32px',
                background: '#F9FAFB',
                borderRadius: '16px',
                boxShadow: '0 2px 8px 0 rgba(16,38,100,0.04)',
                marginBottom: '24px'
            }}
        >
            <Header
                title={headerTitle}
                subtitle={headerSubtitle}
                breadcrumbItems={breadcrumbItems}
                actions={headerActions}
            />
            <div
                className={classNames("main-content-inner", classNameInner)}
                style={{
                    padding: '24px',
                    background: '#fff',
                    borderRadius: '8px',
                    boxShadow: '0 1px 4px 0 rgba(16,38,100,0.03)'
                }}
            >
                {children}
            </div>
        </div>
    )
};

export default Content;
