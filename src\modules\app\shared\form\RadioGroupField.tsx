import React from 'react';
import { Form, FormItemProps, Radio } from 'antd';
import classNames from 'classnames';

interface RadioOption {
    label: string;
    value: string | boolean;
}

interface Props extends FormItemProps {
    className?: string;
    defaultValue?: RadioOption;
    fontSize?: 'default' | 'medium';
    label?: React.ReactNode;
    name: string;
    options: RadioOption[];
    rules?: any[];
}

const RadioGroupField: React.FC<Props> = ({ className, fontSize = 'default', defaultValue, label, name, rules, options, ...props }) => {
    return (
        <Form.Item
            className={classNames("form-field", fontSize, className)}
            name={name}
            label={label}
            rules={rules}
            {...props}
        >
            <Radio.Group className="form-field-radio-wrapper" defaultValue={defaultValue?.value}>
                {options.map((option, key) => (
                    <Radio key={key} value={option.value}>
                        {option.label}
                    </Radio>
                ))}
            </Radio.Group>
        </Form.Item>
    );
};

export default RadioGroupField;
