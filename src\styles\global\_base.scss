body {
    background-color: $color-background;
    color: $color-text;
    font-family: $font-family;
    font-size: $font-size;
    font-weight: $font-weight-regular;
    letter-spacing: $letter-spacing;
    line-height: $line-height;
    text-align: left;
}

h1, h2, h3,
h4, h5, h6 {
    color: $color-heading;
    font-weight: $font-weight-bold;
    line-height: 1.2;
    margin: 0 0 $spacing-lg;
    letter-spacing: $letter-spacing;
}

h1 {
    font-size: 48px;

    @include breakpoint(sm) {
        font-size: 34px;
    }
}

h2 {
    font-size: 32px;

    @include breakpoint(sm) {
        font-size: 26px;
    }
}

h3 {
    font-size: 24px;

    @include breakpoint(sm) {
        font-size: 20px;
    }
}

h4 {
    font-size: 22px;

    @include breakpoint(sm) {
        font-size: 20px;
    }
}

h5 {
    font-size: 20px;

    @include breakpoint(sm) {
        font-size: 18px;
    }
}

h6 {
    font-size: 16px;

    @include breakpoint(sm) {
        font-size: 14px;
    }
}

a {
    @include link-color($color-link-default, $color-link-hover, underline, underline);
    transition: all .2s ease;
    border-radius: $radius-sm;
    &:focus {
        outline: 2px solid $color-primary;
        outline-offset: 2px;
    }
}

p {
    margin: 0 0 $spacing-lg;
    color: $color-text;
    font-size: $font-size;
    line-height: $line-height;
}

b,
bold,
strong {
    font-weight: $font-weight-bold;
    color: $color-heading;
}

hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid $color-hr;
    margin: $spacing-lg auto;
    padding: 0;
}

img, .card {
    border-radius: $radius-md;
}

svg {
    display: block;
}
