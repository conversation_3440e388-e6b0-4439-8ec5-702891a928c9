import React from "react";
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import { EstimateItem } from '../../../../store/claims/types/Claim.ts';
import { Col, Row } from 'antd';
import { formatCurrency } from '../../../../utils/helpers.ts';
import { calculateTotalUnitPrice } from './utils.ts';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    items: EstimateItem[]
}

const ViewEstimateModal: React.FC<Props> = ({items, show, onClose}) => {
    
    const groupedItems = items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
        acc[product.product_family] = acc[product.product_family] || [];
        acc[product.product_family].push(product);
        return acc;
    }, {});
    
    return (
        <Modal
            show={show}
            onClose={onClose}
            size={ModalSize.BIG}
            title="My estimate"
            description="Below is your SPS estimate. Please review it carefully to ensure it aligns with your original estimate. If any adjustments are needed, make edits before final submission."
        >
            <div className="font-weight-bold margin-bottom-16">Your price sheet</div>
            
            <div className="claim-ve-list">
                <div className="claim-ve-list-head">
                    <Row gutter={[8, 8]} align="middle">
                        <Col span={9}>Service title</Col>
                        <Col span={3} className="align-center">Qty</Col>
                        <Col span={6} className="align-right">Price</Col>
                        <Col span={6} className="align-right">Total</Col>
                    </Row>
                </div>
                <div className="claim-ve-list-body">
                    {
                        Object.keys(groupedItems).map((item, index) => {
                            return (
                                <div className="claim-ve-list-body-group" key={index}>
                                    <h6 className="claim-ve-list-body-group-title">
                                        {item}
                                    </h6>
                                    
                                    <div className="claim-ve-list-body-group-content">
                                        {groupedItems[item].map((row, rowIndex) => {
                                            return (
                                                <Row gutter={[8, 8]} className="claim-ve-list-body-group-content-row"
                                                     key={rowIndex}>
                                                    <Col span={9}>{row.product_name}</Col>
                                                    <Col span={3} className="center">{row.quantity}</Col>
                                                    <Col span={6}
                                                         className="right label">{formatCurrency(row.unit_price)}</Col>
                                                    <Col span={6}
                                                         className="right highlight">{formatCurrency(row.unit_price * row.quantity)}</Col>
                                                </Row>
                                            )
                                        })}
                                    </div>
                                </div>
                            )
                        })
                    }
                </div>
                <div className="claim-ve-list-bottom">
                    <h6 className="claim-ve-list-bottom-item label">Total</h6>
                    <h6 className="claim-ve-list-bottom-item highlight">{formatCurrency(calculateTotalUnitPrice(groupedItems))}</h6>
                </div>
            </div>
        </Modal>
    );
};

export default ViewEstimateModal;
