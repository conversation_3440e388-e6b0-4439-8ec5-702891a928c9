import { create } from 'zustand/react';
import { ClaimStore } from './types/ClaimStore.ts';
import { api } from '../../common/api.ts';
import { DEFAULT_PER_PAGE, ParamsField, transition } from '../../@types';

export const claimsStore = create<ClaimStore>((set, get) => ({
    claims: transition.reset([]),
    
    unloadClaims: async () => {
        set({claims: transition.reset([])})
    },
    
    getClaims: async (payload) => {
        set({claims: transition.loading(get().claims.value)})
        const params: ParamsField[] = [];
        if (payload?.status) {
            params.push({
                field: 'status',
                operator: '=',
                value: payload?.status
            })
        }
        const limit = payload?.limit ? payload.limit : DEFAULT_PER_PAGE;
        const direction = payload?.direction ? payload.direction : 'desc';
        const orderBy = payload?.order_by ? payload.order_by : 'submitted_date';
        const response = await api.get(`/claims`, {
            params: {
                params: params,
                search_term: payload?.search_term,
                order_by: orderBy,
                direction: direction,
                limit: limit,
                page: payload?.page
            },
        });
        set({claims: transition.loaded(response.data.data)})
        return response.data;
    },
    
    getClaim: async (id) => {
        const response = await api.get(`/claims/${id}`);
        return response.data.data;
    },
    
    updateClaim: async (id, payload) => {
        const response = await api.post(`/claims/${id}`, payload);
        return response.data.data;
    },
    
    getScheduledDates: async (id) => {
        const response = await api.get(`/claims/${id}/walkthrough-dates`);
        
        return response.data.data;
    },
    
    getClaimPriceBook: async (id) => {
        const response = await api.get(`/claims/${id}/price-book-entries`);
        
        return response.data.data;
    },
    
    getClaimEstimateDocumentLink: async (id, fileName) => {
        const response = await api.post(`/claims/${id}/estimate-document-link`, {
            filename: fileName
        });
        
        return response.data.data;
    },
    
    getEstimate: async (id) => {
        const response = await api.get(`/claims/${id}/estimate`);
        
        return response.data.data;
    },
    
    setEstimate: async (id, payload) => {
        const response = await api.post(`/claims/${id}/estimate`, payload);
        
        return response.data.data;
    }
}));
