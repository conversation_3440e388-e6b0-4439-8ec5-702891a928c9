import React from 'react';
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import Button from '../../../app/shared/Button.tsx';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onNo: VoidFunction;
    onYes: VoidFunction;
}

const EstimateConfirmationModal: React.FC<Props> = ({
    show,
    onClose,
    onNo,
    onYes,
}) => {
    return (
        <Modal
            show={show}
            onClose={onClose}
            size={ModalSize.MEDIUM}
            title="Estimate Services Uploaded!"
        >
            <div className="estimate-confirmation-content">
                <p className="estimate-confirmation-description">
                    Have you completed adding all services related to this project? If so, please preview the estimate before confirming submission.
                </p>
                
                <div className="estimate-confirmation-actions">
                    <Button
                        color="default"
                        variant="outlined"
                        onClick={onNo}
                        className="estimate-confirmation-no-button"
                    >
                        NO
                    </Button>
                    <Button
                        color="primary"
                        variant="solid"
                        onClick={onYes}
                        className="estimate-confirmation-yes-button"
                    >
                        YES
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default EstimateConfirmationModal; 