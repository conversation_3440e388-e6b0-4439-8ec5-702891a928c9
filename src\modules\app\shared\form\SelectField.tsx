import React from 'react';
import { Form, FormItemProps, Select, SelectProps } from 'antd';
import classNames from 'classnames';
import Chevron from '../svg/Chevron.tsx';

export interface SelectFormOption {
    label: string;
    value: string;
}

interface Props extends FormItemProps {
    className?: string;
    defaultValue?: SelectFormOption;
    disabled?: boolean;
    fontSize?: 'default' | 'medium';
    label?: string;
    name: string;
    onOptionChange?: (option: string) => void;
    options: SelectFormOption[];
    placeholder?: string;
    rules?: any[];
    selectProps?: SelectProps;
    type?: string;
}

const SelectField: React.FC<Props> = ({
    className,
    defaultValue,
    disabled,
    fontSize = 'default',
    label,
    name,
    onOptionChange,
    options,
    placeholder,
    rules,
    selectProps,
    ...props
}) => {
    return (
        <Form.Item
            className={classNames("form-field", className)}
            name={name}
            label={label}
            rules={rules}
            {...props}
        >
            <Select
                className={classNames("form-field-select", fontSize)}
                style={{ width: 120 }}
                disabled={disabled}
                onChange={(value) => !!onOptionChange && onOptionChange(value)}
                placeholder={placeholder}
                defaultValue={defaultValue?.value}
                popupClassName="form-field-select-popup"
                options={options}
                suffixIcon={<Chevron className="icon-regular" direction="down" />}
                {...selectProps}
            />
        </Form.Item>
    );
};

export default SelectField;
