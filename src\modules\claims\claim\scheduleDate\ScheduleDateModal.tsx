import React, { useState } from "react";
import Modal from '../../../app/shared/Modal.tsx';
import AvailableDates from './AvailableDates.tsx';
import FreeDatesModal from './FreeDatesModal.tsx';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { Claim, WalkthroughType } from '../../../../store/claims/types/Claim.ts';
import { useClaimStore } from '../../../../store';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';

interface Props {
    show: boolean;
    onClose: VoidFunction;
}

const ScheduleDateModal: React.FC<Props> = ({ show, onClose }) => {
    const { currentClaim, setCurrentClaim } = useClaimsContext();
    const { updateClaim } = useClaimStore();
    const [submitting, setSubmitting] = useState<boolean>(false);
    
    const handleUpdateScheduleDate = (date: string, time: string) => {
        if (!currentClaim?.id) {
            return;
        }
        setSubmitting(true);
        
        updateClaim(currentClaim.id, { walkthrough_scheduled_date: date, walkthrough_scheduled_time: time }).then((claim: Claim) => {
            setCurrentClaim(claim);
            FlashMessages.success('Success!');
            onClose();
        }).catch((e) => {
            const message = e.response?.data?.message ? e.response?.data?.message : e.response?.data
            e.response?.data?.message && FlashMessages.error(message);
        }).finally(() => {
            setSubmitting(false);
        })
    }
    
    const getDescription = (claim?: Claim) => {
        if (!claim) {
            return;
        }
        
        if (currentClaim?.walkthrough_type === WalkthroughType.AVAILABLE_DATES) {
            return ({
                title: 'Schedule for Walkthrough',
                description: 'Below are the available dates for the walkthrough. Select a date that works for you and click "Set Schedule" to confirm.'
            })
        }
        
        if (currentClaim?.walkthrough_type === WalkthroughType.VENDOR_SCHEDULES) {
            return ({
                title: 'Schedule for Walkthrough',
                description: <>Call the homeowner using the contact information below. Record the walkthrough date <b>only after it is confirmed by the homeowner</b>, then click "Save" to continue.</>
            })
        }
        
        return ({
            title: '',
            description: ''
        })
    }
    
    return (
        <Modal
            show={show}
            onClose={onClose}
            title={getDescription(currentClaim)?.title}
            description={getDescription(currentClaim)?.description}
        >
            {currentClaim?.walkthrough_type === WalkthroughType.AVAILABLE_DATES &&
                <AvailableDates disableForm={submitting} onSubmitDate={(date, time) => handleUpdateScheduleDate(date, time)}/>}
            {currentClaim?.walkthrough_type === WalkthroughType.VENDOR_SCHEDULES &&
                <FreeDatesModal disableForm={submitting} onSubmitDate={(date, time) => handleUpdateScheduleDate(date, time)}/>}
        </Modal>
    );
};

export default ScheduleDateModal;
