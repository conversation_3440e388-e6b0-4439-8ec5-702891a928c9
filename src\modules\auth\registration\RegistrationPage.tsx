import AuthLayout from '../../app/layouts/AuthLayout.tsx';
import withoutAuth from '../../../hooks/withoutAuth.tsx';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useInvitationStore } from '../../../store';
import PersonalInfo from './PersonalInfo.tsx';
import { InvitationStep } from '../../../store/invitation/types/InvitationData.ts';
import CompanyInitial from './CompanyInitial.tsx';
import CompanySecondary from './CompanySecondary.tsx';

const RegistrationPage = () => {
    const {checkToken, personalData, getCompanyData} = useInvitationStore();
    const [step, setStep] = useState<InvitationStep | undefined>(personalData?.step);
    const token = (new URLSearchParams(window.location.search)).get('token');
    const [visible, setVisible] = useState<boolean>(false);
    const navigate = useNavigate();
    
    useEffect(() => {
        if (!token) {
            return;
        }
        
        checkToken(token).then((r) => {
            setStep(r.step);
            setVisible(true);
        }).catch(() => {
            navigate('/auth')
        });
    }, [checkToken, token]);
    
    useEffect(() => {
        if (!token || !visible) {
            return;
        }

        getCompanyData(token);
    }, [getCompanyData, token, visible]);
    
    if (!token || !visible) {
        return null;
    }
    
    return (
        <AuthLayout
            className="auth-register"
            subtitle={ !step ? "Create an account to continue" : undefined}
            title={ !step ? "Create Account" : undefined}
            variation={!step ? 'center' : 'side'}
            authSideContent={
                !!step &&
                <div className="auth-register-side">
                    <h2 className="heading h2">Transparency and safety are at our core</h2>
                    <p className="auth-register-side-text">We aim to meet YOUR needs</p>
                </div>
            }
        >
            { !step && <PersonalInfo token={token} onStepChange={(step) => setStep(step)} /> }
            
            { step === InvitationStep.STEP_1 && <CompanyInitial token={token} onStepChange={(step) => setStep(step)} /> }
            
            { step === InvitationStep.STEP_2 && <CompanySecondary token={token} onStepChange={(step) => setStep(step)} /> }
            
        </AuthLayout>
    );
};

export default withoutAuth(RegistrationPage);
