import { AuthStore } from './types/AuthStore.ts';
import { create } from 'zustand/react';
import { LoginData, PasswordChange, PasswordConfirmation } from './types/AuthData.ts';
import { createJSONStorage, persist } from 'zustand/middleware';
import { api, publicApi } from '../../common/api.ts';

export const authStore = create<AuthStore>()(
    persist(
        (set, get) => ({
            refreshToken: null,
            expiresAt: null,
            _retry: false,
            accessToken: null,
            user: null,
            
            isAuthenticated: () => get().accessToken !== null && !!get().user,
            
            login: async (data: LoginData) => {
                try {
                    const response = await publicApi.post('/auth/login', data);
                    set({ user: response.data.data.user, expiresAt: response.data.data.expires_at, accessToken: response.data.data.token });
                } catch (error) {
                    return Promise.reject(error);
                }
            },
            logout: async () => {
                get().unload();
                // set((state) => ({ ...state }));
            },
            unload: () => {
                set({ accessToken: null, expiresAt: null, _retry: false, user: null, refreshToken: null });
            },
            refresh: async () => {
                const response = await publicApi.post('/auth/refresh', undefined, {
                    headers: {
                        Authorization: `Bearer ${get().accessToken}`,
                    },
                });
                set({ user: response.data.data.user, expiresAt: response.data.data.expires_at, accessToken: response.data.data.token });
            },
            forgotPassword: async (email) => {
                await publicApi.post('/auth/forgot-password', {
                    email: email
                });
            },
            resetPassword: async (data: PasswordConfirmation) => {
                await publicApi.post(`/auth/reset-password`, {
                    password: data.password,
                    email: data.email,
                    token: data.token
                });
            },
            changePassword: async (data: PasswordChange) => {
                try {
                    const response = await api.post(`/auth/change-password`, data);
                    return response.data;
                } catch (e) {
                    return Promise.reject(e);
                }
            },
        }),
        {
            name: 'auth-storage',
            storage: createJSONStorage(() => localStorage),
        }
    )
);
