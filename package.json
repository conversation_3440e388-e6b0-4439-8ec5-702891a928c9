{"name": "sps", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "antd": "^5.21.0", "axios": "^1.7.7", "class-transformer": "^0.5.1", "classnames": "^2.5.1", "date-fns": "^4.1.0", "just-debounce-it": "^3.2.0", "react": "^18.3.1", "react-datepicker": "^7.4.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-pdf": "^9.1.1", "react-router-dom": "^6.26.2", "react-toastify": "^10.0.5", "reflect-metadata": "^0.2.2", "vite-plugin-static-copy": "^1.0.6", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.7.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.11.1", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.36.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "sass": "^1.79.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.2.0"}}