import {createRoot} from 'react-dom/client'
import './styles/style.scss'
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import App from './modules/app/App.tsx';
import { ConfigProvider } from 'antd';
import theme from './modules/app/ThemeConfig.ts';

createRoot(document.getElementById('root')!).render(
    <>
        <ConfigProvider theme={theme}>
            <App/>
            <ToastContainer
                pauseOnHover={false}
                position="top-right"
                autoClose={4000}
                limit={4}
                newestOnTop
                hideProgressBar={true}
            />
        </ConfigProvider>
    </>,
)
