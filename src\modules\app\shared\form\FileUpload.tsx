import React, { useState } from 'react';
import { Form, FormInstance, Progress, Upload, UploadFile } from 'antd';
import { UploadProps } from 'antd/es/upload';
import Button from '../Button.tsx';
import classNames from 'classnames';
import { UploadChangeParam } from 'antd/lib/upload';
import {ReactComponent as FileIcon} from '../../../../assets/icons/icon-file.svg';
import {ReactComponent as IconClose} from '../../../../assets/icons/icon-close.svg';

interface FileUploadProps extends UploadProps {
    actionUrl?: string;
    children?: React.ReactNode;
    className?: string;
    form?: FormInstance; // it is mandatory for upload fields without progress bar
    label?: string;
    maxFileSize?: number;
    name: string;
    onCustomRemove?: VoidFunction;
    onFinish?: (state: boolean) => void;
    optionalLabel?:  boolean;
    rules?: any[];
    selectedFile?: (file: File) => void;
    showProgressBar?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
    actionUrl,
    children,
    className,
    form,
    label,
    maxFileSize,
    name,
    onCustomRemove,
    onFinish,
    optionalLabel = false,
    rules,
    selectedFile,
    showProgressBar = false,
    ...uploadProps
}) => {
    const [fileInfo, setFileInfo] = useState<{ name: string; size: number } | null>(null);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [uploadPercent, setUploadPercent] = useState<number>(0);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const [error, setError] = useState<string>();
    
    const normFile = (e: any) => {
        if (Array.isArray(e)) {
            return e;
        }
        return e?.fileList;
    };
    
    const handleChange = (info: UploadChangeParam<UploadFile>) => {
        const { file } = info;
        setError(undefined);
        
        if (!file) {
            return;
        }
        
        if (!!info.fileList.length && info.fileList[0].originFileObj) {
            !!selectedFile && selectedFile(info.fileList[0].originFileObj);
        }
        
        if (file) {
            setFileInfo({
                name: file.name,
                size: (file.size || 0) / 1024 / 1024,
            });
        }
        
        setFileList([file]);
    };
    
    const customRequest = async (options: any) => {
        const { onProgress, onSuccess, onError } = options;
        setIsUploading(true);
        !!onFinish && onFinish(false);
        
        const mockUpload = () => {
            let percent = 0;
            const interval = setInterval(() => {
                percent += 10;
                setUploadPercent(percent);
                onProgress({ percent });
                
                if (percent >= 100) {
                    clearInterval(interval);
                    setIsUploading(false);
                    onSuccess("File uploaded successfully");
                    !!onFinish && onFinish(true);
                    setUploadPercent(0);
                    
                }
            }, 300);
        };
        
        
        try {
            mockUpload();
        } catch (error) {
            console.error(error);
            setIsUploading(false);
            onError("Failed to upload");
        }
    };
    
    const sizeLimit = (file: File) => {
        if (!maxFileSize) {
            return true;
        }
        const isValidSize = file.size / 1024 / 1024 <= maxFileSize;
        if (!isValidSize) {
            setError(`File size exceeds the limit of ${maxFileSize}MB`);
            form?.setFieldsValue({ [name]: undefined });
            return Upload.LIST_IGNORE;
        }
        return true;
    };
    
    return (
        <>
            <Form.Item
                label={optionalLabel ? <>{label}<span className="info">Optional</span></> : label}
                className={classNames('form-field', className)}
            >
                <Form.Item
                    name={name}
                    valuePropName="fileList"
                    getValueFromEvent={normFile}
                    noStyle
                    rules={rules}
                    className="form-field-upload"
                >
                    <>
                        <Upload.Dragger
                            accept=".png,.jpg,.jpeg,.pdf"
                            action={actionUrl}
                            customRequest={customRequest}
                            onChange={handleChange}
                            beforeUpload={maxFileSize ? sizeLimit : () => false}
                            className="form-field-upload-dragger"
                            fileList={fileList}
                            name="files"
                            maxCount={1}
                            prefixCls="form-field-upload-dragger-inner"
                            {...uploadProps}
                        >
                            {children || (
                                <div className="form-field-upload-dragger-content">
                                    <div className="form-field-upload-dragger-content-body">
                                        <div className="form-field-upload-dragger-content-body-title">
                                            Drag & Drop or <span>Choose file</span> to upload
                                        </div>
                                        <div className="form-field-upload-dragger-content-body-label">
                                            JPG, PNG or PDF, file size no more than 10MB
                                        </div>
                                    </div>
                                    <div className="form-field-upload-dragger-content-side">
                                        <Button size="large" color="primary" htmlType="button">Select File</Button>
                                    </div>
                                </div>
                            )}
                        </Upload.Dragger>
                    </>
                </Form.Item>
            </Form.Item>
            {error && <div className="form-field-upload-error">{error}</div>}
            {
                showProgressBar && !!fileList.length &&
                <div className="form-field-upload-progress">
                    <h6 className="form-field-upload-progress-label">File added</h6>
                    <div className="form-field-upload-progress-inner">
                        <div className="form-field-upload-progress-icon">
                            <FileIcon className="icon-regular"/>
                        </div>
                        <div className="form-field-upload-progress-content">
                            <div className="form-field-upload-progress-content-top">
                                <div className="highlight">{fileInfo?.name}</div>
                                <div className="label">{fileInfo?.size && <>{fileInfo?.size.toFixed(2)} MB</>}</div>
                            </div>
                            {isUploading &&
                                <Progress className="form-field-upload-progress-content-bar" showInfo={false}
                                          percent={uploadPercent}/>}
                        </div>
                        <IconClose
                            onClick={() => {
                                setFileList([]);
                                !!onCustomRemove && onCustomRemove();
                            }}
                            className="form-field-upload-progress-content-remove"
                        />
                    </div>
                </div>
            }
        </>
    
    );
};

export default FileUpload;
