# Safe Pack and Store

This is a React Vite project. Written with [TypeScript](https://www.typescriptlang.org/). You can use [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/).

## Getting Started

### Environment Setup

To run the project, you'll need to set up environment variables.

### Copy `.env.example`

Run this command to create a `.env` file from the example template:

```bash
cp .env.example .env
```

### Populate `.env`

Populate `.env` with correct values depending on environment

### Install all packages and requirements

```bash
npm install
# or
yarn install
```

### Development

```bash
npm run dev
# or
yarn dev
```
Open [http://localhost:3600](http://localhost:3600) with your browser to see the result.

### Build

```bash
npm run build
# or
yarn build
```

Builds the app for production to the `dist` folder.\
