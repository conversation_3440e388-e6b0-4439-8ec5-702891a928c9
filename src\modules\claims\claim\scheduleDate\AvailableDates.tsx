import React, { useCallback, useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import { format, isSameDay, parseISO, parse } from "date-fns";
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Col, Form, Row } from 'antd';
import SelectField, { SelectFormOption } from '../../../app/shared/form/SelectField.tsx';
import Button from '../../../app/shared/Button.tsx';
import { DATE_FORMAT_MONTHNAME } from '../../../../common/dateFormats.ts';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { useClaimStore } from '../../../../store';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';
import Loader from '../../../app/shared/Loader.tsx';

interface Props {
    disableForm: boolean;
    onSubmitDate: (date: string, time: string) => void;
}

const AvailableDates: React.FC<Props> = ({disableForm, onSubmitDate}) => {
    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [formattedDates, setFormattedDates] = useState<Date[]>([])
    const [selectFieldDates, setSelectFieldDates] = useState<SelectFormOption[]>([])
    const [selectFieldTimeDates, setSelectFieldTimeDates] = useState<SelectFormOption[]>([])
    const [dates, setDates] = useState<{ [key: string]: string[] }>({})
    const [form] = Form.useForm();
    const [visible, setVisible] = useState<boolean>(false);
    const {currentClaim} = useClaimsContext();
    const {getScheduledDates} = useClaimStore();
    const [loaded, setLoaded] = useState<boolean>(false);
    
    const getDates = useCallback(() => {
        if (!currentClaim) {
            return;
        }
        getScheduledDates(currentClaim.id).then((r) => {
            const tempDates: { [key: string]: string[] } = {};
            r.forEach((item) => {
                const formattedDate = item.date.split('T')[0];
                
                if (!tempDates[formattedDate]) {
                    tempDates[formattedDate] = [];
                }
                
                tempDates[formattedDate].push(item.time);
            });
            setDates(tempDates);
            
            const formatDates: Date[] = Object.keys(tempDates).map((item) => parseISO(item));
            
            setFormattedDates(formatDates);
            
            const selectFieldsOptions: SelectFormOption[] = formatDates.map((item: Date) => {
                return ({
                    label: format(item, DATE_FORMAT_MONTHNAME),
                    value: item.toString()
                })
            });
            
            setSelectFieldDates(selectFieldsOptions);
            
            if (selectFieldsOptions.length > 0) {
                form.setFieldsValue({
                    date: selectFieldsOptions[0].value,
                });
                setSelectedDate(new Date(selectFieldsOptions[0].value));
            }
        }).catch(() => {
            FlashMessages.error('Something went wrong, please try again!');
        }).finally(() => {
            setLoaded(true);
            setVisible(true);
        });
    }, [form]);
    
    const handleChange = (date: Date | null) => {
        if (!date) {
            return;
        }
        const formattedValue = selectFieldDates.find((item) => item.label === format(date, DATE_FORMAT_MONTHNAME))?.value
        if (!formattedValue) {
            return;
        }
        setSelectedDate(new Date(formattedValue));
        form.setFieldValue('date', format(formattedValue, DATE_FORMAT_MONTHNAME));
    };
    
    const customDayClassName = (date: Date) => {
        return formattedDates.some((d) => isSameDay(d, date))
            ? "highlighted"
            : "";
    };
    
    const isDateSelectable = (date: Date) => {
        return formattedDates.some((d) => isSameDay(d, date));
    };
    
    const onSubmit = (fields: { date: string, time: string }) => {
        const date = new Date(fields.date);
        
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        
        const formattedDate = `${year}-${month}-${day}`;
        const shortenedTime = fields.time.substring(0, 5);
        
        onSubmitDate(formattedDate, shortenedTime);
    }

    useEffect(() => {
            getDates();
    }, [getDates]);
    
    useEffect(() => {
        if (!selectFieldDates.length) {
            setVisible(false)
            return;
        }
        setVisible(true);
    }, [selectFieldDates]);
    
    useEffect(() => {
        if (!selectedDate) {
            return;
        }
        const formattedDate = format(selectedDate, 'yyyy-MM-dd');
        
        const timeDates: SelectFormOption[] = (dates[formattedDate] || []).map((item) => {
            const timeDate = parse(item, 'HH:mm:ss', new Date());
            const formattedTime = format(timeDate, 'h:mm aa');
            
            return {
                label: formattedTime,
                value: item,
            };
        });
        
        setSelectFieldTimeDates(timeDates);

        form.setFieldValue('time', timeDates[0].value)
        form.setFieldValue('date', format(selectedDate, DATE_FORMAT_MONTHNAME));
    }, [selectedDate]);
    
    if (!visible && !loaded) {
        return <Loader />
    }
    
    return (
        <CustomForm
            form={form}
            onSubmit={onSubmit}
        >
            {!!formattedDates.length &&
                <>
                    <div className="font-weight-bold margin-bottom-16">Available dates:</div>
                    <DatePicker
                        calendarClassName="margin-bottom-24"
                        selected={selectedDate}
                        onChange={handleChange}
                        dayClassName={customDayClassName}
                        inline
                        filterDate={isDateSelectable}
                    />
                    
                    <Row gutter={[16, 16]}>
                        <Col span={12}>
                            <SelectField
                                className="margin-bottom-16"
                                initialValue={selectFieldDates[0]}
                                onOptionChange={(value) => setSelectedDate(new Date(value))}
                                name="date"
                                options={selectFieldDates}
                                selectProps={{
                                    size: 'small'
                                }}
                            />
                        </Col>
                        <Col span={12}>
                            <SelectField
                                className="margin-bottom-16"
                                initialValue={selectFieldTimeDates[0]?.value}
                                name="time"
                                options={selectFieldTimeDates}
                                onOptionChange={(value) => {
                                    form.setFieldValue('time', value );
                                }}
                                selectProps={{
                                    size: 'small'
                                }}
                            />
                        </Col>
                    </Row>
                    
                    <Button
                        color="primary"
                        variant="solid"
                        disabled={disableForm}
                        wide={true}
                    >
                        Set a Schedule
                    </Button>
                </>
            }
            
            {!formattedDates.length && <p>No available dates.</p>}
        </CustomForm>
    );
};

export default AvailableDates;
