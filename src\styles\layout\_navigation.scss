.navigation {
    @include flex(flex, column, nowrap, flex-start, stretch);
    flex: 1;
    gap: $spacing-lg;
    height: 100%;

    &-item {
        @include link-color($color-primary, $color-primary-dark, none, none);
        @include flex(flex, row, nowrap, flex-start, center);
        gap: $spacing-md;
        border-radius: $radius-md;
        color: $color-primary;
        cursor: pointer;
        font-size: $font-size;
        font-weight: $font-weight-semibold;
        line-height: 1.5;
        padding: $spacing-md $spacing-lg;
        transition: background 0.2s, color 0.2s;
        text-decoration: none;
        position: relative;
        outline: none;

        &:active,
        &:focus,
        &:visited {
            color: $color-primary-dark;
            text-decoration: none;
        }

        &:hover {
            background-color: $color-surface;
            color: $color-primary-dark;
            text-decoration: none;
        }

        &.static {
            cursor: default;
            background: none;
            color: $color-text-light;
            &:hover {
                background-color: transparent;
                color: $color-text-light;
            }
        }

        &.active {
            background-color: $primary-50;
            color: $color-primary-dark;
            font-weight: $font-weight-bold;
            .icon.marked {
                &::before {
                    background-color: $primary-50;
                    z-index: 1;
                }
            }
        }

        .icon {
            font-size: 22px;
            color: $color-primary;
            @include breakpoint(md) {
                margin-bottom: 0;
            }
        }
    }

    &-group {
        &-content {
            padding-left: $spacing-xl;
            .navigation-item {
                font-size: $font-size-sm;
                font-weight: $font-weight-regular;
            }
        }
    }

    &-end {
        margin-top: auto;
    }
}
