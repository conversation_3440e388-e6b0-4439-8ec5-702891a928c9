import React from 'react';
import { Breadcrumb } from 'antd';
import Chevron from '../../shared/svg/Chevron.tsx';
import { BreadcrumbItemType } from 'antd/lib/breadcrumb/Breadcrumb';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';

interface Props {
    actions?: React.ReactNode;
    breadcrumbItems?: BreadcrumbItemType[];
    subtitle?: string;
    title?: React.ReactNode;
}

const Header: React.FC<Props> = ({ actions, breadcrumbItems, subtitle, title }) => {
    const navigate = useNavigate();
    return (
        <header
            className={classNames("header", {
                secondary: !!breadcrumbItems?.length
            })}
            style={{
                background: '#fff',
                boxShadow: '0 2px 8px 0 rgba(16,38,100,0.04)',
                borderRadius: '0 0 16px 16px',
                padding: '24px 32px 16px 32px',
                marginBottom: '24px'
            }}
        >
            {!!breadcrumbItems?.length && (
                <Breadcrumb
                    className="breadcrumb"
                    separator={<Chevron direction="right" className="icon-regular"/>}
                    items={breadcrumbItems.map((item) => ({
                        title: (
                            <span
                                className="ant-breadcrumb-link"
                                onClick={() => navigate(`${item.href}`)}
                                style={{ color: '#3366FF', fontWeight: 500 }}
                            >
                                {item.title}
                            </span>
                        ),
                    }))}
                />
            )}
            {((!!actions || !!title || !!subtitle)) && (
                <div className="header-content" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '32px' }}>
                    <div className="header-content-meta">
                        {typeof title === 'string' ? (
                            <h1 className="heading h2" style={{ marginBottom: 0 }}>{title}</h1>
                        ) : (
                            title
                        )}
                        {subtitle && <p className="header-content-meta-subtitle" style={{ color: '#6B7280', fontSize: '18px', margin: '8px 0 0 0' }}>{subtitle}</p>}
                    </div>
                    {actions && (
                        <div className="header-content-actions" style={{ display: 'flex', gap: '16px' }}>
                            {actions}
                        </div>
                    )}
                </div>
            )}
        </header>
    );
};

export default Header;
