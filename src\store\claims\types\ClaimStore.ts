import {
    Claim,
    ClaimUpdateFields,
    Estimate,
    EstimateCreate,
    PriceBookItem,
    QueryFields,
    ScheduledTimeItem
} from './Claim.ts';
import { FileUrlData, PaginationMeta, Resource } from '../../../@types';

export interface ClaimStore {
    claims: Resource<Claim[]>;
    unloadClaims: () => void;
    
    getClaims: (payload?: QueryFields) => Promise<{ data: Claim[], meta: PaginationMeta }>;
    getClaim: (id: string) => Promise<Claim>;
    getClaimPriceBook: (id: string) => Promise<PriceBookItem[]>;
    getClaimEstimateDocumentLink: (id: string, fileName: string) => Promise<FileUrlData>;
    updateClaim: (id: string, claimFields: Partial<ClaimUpdateFields>) => Promise<Claim>;
    getScheduledDates: (id: string) => Promise<ScheduledTimeItem[]>;
    setEstimate: (id: string, payload: EstimateCreate) => Promise<Estimate>;
    getEstimate: (id: string) => Promise<Estimate>;
}
