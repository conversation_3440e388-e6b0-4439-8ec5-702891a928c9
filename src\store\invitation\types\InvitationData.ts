import { FileUrlData, PaginationMeta } from '../../../@types';
import { UploadFile } from 'antd';
import { SelectFormOption } from '../../../modules/app/shared/form/SelectField.tsx';

export enum InvitationStep {
    STEP_1 = 'step-1',
    STEP_2 = 'step-2',
    STEP_3 = 'step-3',
}
export interface InvitationPersonalData {
    name: string,
    email: string,
    password: string,
    step: InvitationStep
}

export interface InvitationCompanyData {
    form: InvitationCompanyFormData,
    options?: {
        warehouse_method_of_storage: { label: string, value: string }[],
        certificate_of_insurance: FileUrlData,
        coverage_options_for_clients: FileUrlData,
        liability_and_cargo_insurance: FileUrlData
    }
}


export interface InvitationCompanyFormData {
    background_checks?: string,
    business_address?: string;
    business_city?: string;
    business_email?: string;
    business_license_number?: string;
    business_phone?: string;
    business_point_of_contact?: SelectFormOption | string | undefined;
    business_zip?: string;
    certificate_of_insurance?: string,
    certificate_of_insurance_file?: UploadFile[],
    coverage_options_for_clients_file?: UploadFile[],
    coverage_options_for_clients?: string,
    liability_and_cargo_insurance_file?: UploadFile[],
    liability_and_cargo_insurance?: string,
    collect_mvr?: string,
    company_name?: string;
    employee_drug_screen?: string,
    signature?: string,
    usdot_number?: string,
    warehouse_business_address?: string,
    warehouse_city?: string,
    warehouse_climate_controlled?: boolean,
    warehouse_contents_trough_inventory?: string,
    warehouse_method_of_storage?: string[],
    warehouse_zip?: string,
}

export interface InvitationBusinessContacts {
    data: {
        name: string,
        id: string
    }[],
    meta: PaginationMeta
}
