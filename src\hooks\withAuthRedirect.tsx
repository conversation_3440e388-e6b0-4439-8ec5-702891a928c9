import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../store';

interface WithProps {
    WrappedComponent: React.ComponentType;
    expectedAuth: boolean;
    location: string;
}

export default function withAuthRedirect({ WrappedComponent, expectedAuth, location }: WithProps) {
    return (props: any) => {
        const isAuthenticated = useAuthStore((state) => state.isAuthenticated());
        
        if (expectedAuth !== isAuthenticated) {
            return <Navigate to={location} replace />;
        }

        return <WrappedComponent {...props} />;
    };
}
