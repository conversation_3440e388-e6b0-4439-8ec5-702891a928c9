.table {
    border: 1px solid #E1E2E4;
    border-radius: 4px;

    .ant {
        &-table {
            &-title {
                padding: 22px 16px;
            }

            &-thead {
                .ant-table-cell {
                    font-size: 14px;
                    font-weight: $font-weight-bold;
                    padding: 14px 8px;

                    &:first-child {
                        padding-left: 16px;
                    }

                    &:last-child {
                        padding-right: 16px;
                    }

                    &:before {
                        content: none !important;
                    }
                }
            }

            &-tbody {
                .ant-table-cell {
                    padding: 14px 8px;

                    &:first-child {
                        padding-left: 16px;
                    }

                    &:last-child {
                        padding-right: 16px;
                    }
                }
            }

            &-footer {
                background-color: transparent;
                padding: 14.5px 16px;
            }

            &-column {

                &-title {
                    .head {
                        @include flex(flex, row, nowrap, flex-start, center);
                        gap: 4px;

                        &-title {

                        }

                        &-sort {
                            @include flex(flex, row, nowrap, flex-start, center);

                            &-item {
                                font-size: 18px;

                                [aria-sort='ascending'] & {
                                    background-color: red;
                                }

                                &.asc {
                                    margin-left: -5px;
                                }

                                &.desc {
                                    margin-right: -5px;
                                }
                            }
                        }
                    }
                }

                &-sort {
                    &[aria-sort='ascending'] {

                        .head-sort-item {
                            &.asc {
                                color: $accent-400;
                            }
                        }
                    }

                    &[aria-sort='descending'] {
                        .head-sort-item {
                            &.desc {
                                color: $accent-400;
                            }
                        }
                    }
                }

                &-sorter {
                    display: none;
                }
            }
        }
    }


    // custom classes
    &-header {
        @include flex(flex, row, nowrap, flex-start, center);
        gap: 16px;

        &-title {
            margin-bottom: 0;
        }
    }

    &-footer {
        @include flex(flex, row, nowrap, space-between, center);
        gap: 16px;

        &-actions {
            li {
                height: auto !important;
            }
        }
    }
}
