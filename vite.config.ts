import {defineConfig} from 'vite'
import react from '@vitejs/plugin-react'
import svgr from 'vite-plugin-svgr'
import {viteStaticCopy} from 'vite-plugin-static-copy';

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        react(),
        svgr({
            svgrOptions: {exportType: 'named', ref: true, svgo: false, titleProp: true},
            include: '**/*.svg',
        }),
        viteStaticCopy({
            targets: [
                {
                    src: './src/assets/*',
                    dest: 'assets/',
                },
            ],
        }),
    ],
    
    server: {
        port: 3600,
        host: 'localhost',
    },
    
    preview: {
        port: 3601,
        host: 'localhost',
    },
})
