.align {

    &-left {
    	text-align: left;
    }

    &-right {
    	text-align: right;
    }

    &-center {
    	text-align: center;
    }

    &-justify {
    	text-align: justify;
    }

}

.text {

    &-uppercase {
    	text-transform: uppercase;
    }

    &-lowercase {
    	text-transform: lowercase;
    }

    &-capitalize {
    	text-transform: capitalize;
    }

	&-medium {
		font-size: 18px;
		font-weight: $font-weight-semibold;
	}

}


.margin {

	@each $position in $positions {

		&-#{$position} {

			@each $value in $values {

				&-#{$value} {
					margin-#{$position}: #{$value}px;
				}

			}

		}

	}

}

.padding {

	@each $position in $positions {

		&-#{$position} {

			@each $value in $values {

				&-#{$value} {
					padding-#{$position}: #{$value}px;
				}

			}

		}

	}

}

.responsive-img {
    @include responsive-img();
}

.mobile {

    &-hide {

        @include breakpoint(sm) {
            display: none !important;
        }

    }

    &-show {

        display: none;

        @include breakpoint(sm) {
            display: block;
        }

    }

}

.flex {

	&-start {
		@include flex(flex, row, wrap, flex-start, center);
	}

	&-end {
		@include flex(flex, row, wrap, flex-end, center);
	}

	&-center {
		@include flex(flex, row, wrap, center, center);
	}

	&-left {
		@include flex(flex, row, wrap, left, center);
	}

	&-right {
		@include flex(flex, row, wrap, right, center);
	}

	&-space {

		&-between {
			@include flex(flex, row, wrap, space-between, center);
		}

		&-around {
			@include flex(flex, row, wrap, space-around, center);
		}

		&-evenly {
			@include flex(flex, row, wrap, space-evenly, center);
		}

	}

}

.icon {

	&-regular {
		font-size: 24px;
	}

	&-small {
		font-size: 20px;
	}

	&-tiny {
		font-size: 16px;
	}

	&-extratiny {
		font-size: 8px;
	}
}

.font {

	&-medium {
		font-size: 18px;
	}

	&-regular {
		font-size: 16px;
	}

	&-small {
		font-size: 14px;
	}

	&-weight {

		&-semibold {
			font-weight: $font-weight-semibold;
		}

		&-bold {
			font-weight: $font-weight-bold;
		}
	}
}

.cursor {
	cursor: pointer;
}

.tag {
	@include flex(inline-flex, row, wrap, flex-start, center);
	background-color: #EFEFEF;
	border-radius: 40px;
	color: rgba($color-primary, .6);
	font-size: 12px;
	font-weight: $font-weight-bold;
	padding: 4px 8px;

	&.warn {
		background-color: #F5B5B5;
		color: $color-text;
	}

	&.pending {
		background-color: #F5EEB5;
		color: $color-text;
	}

	&.success {
		background-color: #B5F5EC;
		color: $color-text;
	}
}

.color {

	&-error {
		color: $color-error;
	}
}
