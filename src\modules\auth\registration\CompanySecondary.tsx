import { Col, Form, Row } from 'antd';
import Button, { ButtonSize, ButtonVariation } from '../../app/shared/Button.tsx';
import CustomForm from '../../app/shared/form/Form.tsx';
import React, { useEffect, useState } from 'react';
import InputField from '../../app/shared/form/InputField.tsx';
import Arrow from '../../app/shared/svg/Arrow.tsx';
import SelectField from '../../app/shared/form/SelectField.tsx';
import RadioGroupField from '../../app/shared/form/RadioGroupField.tsx';
import FileUpload from '../../app/shared/form/FileUpload.tsx';
import { InvitationCompanyFormData, InvitationStep } from '../../../store/invitation/types/InvitationData.ts';
import { useInvitationStore, useFileStore } from '../../../store';
import { useNavigate } from 'react-router-dom';
import FlashMessages from '../../app/shared/FlashMessages.tsx';

interface Props {
    onStepChange: (step?: InvitationStep) => void;
    token: string;
}

const CompanySecondary: React.FC<Props> = ({ onStepChange, token }) => {
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const {setCompanyDetailsStep, invitationCompanyData} = useInvitationStore();
    const {uploadFileS3} = useFileStore();
    const [formLoading, setFormLoading] = useState<boolean>(false);
    
    const onFinish = async (values: Partial<InvitationCompanyFormData>) => {
        setFormLoading(true);
        try {
            if (!values?.certificate_of_insurance_file || !values?.certificate_of_insurance_file.length) {
                return;
            }
            await form.validateFields();
            await uploadFileS3(values?.certificate_of_insurance_file[0]?.originFileObj as File, invitationCompanyData?.options?.certificate_of_insurance.presigned_url || '')
            delete values.certificate_of_insurance_file;
            values['certificate_of_insurance'] = invitationCompanyData?.options?.certificate_of_insurance.url || ''
            
            if (!!values?.liability_and_cargo_insurance_file?.length && invitationCompanyData?.options?.liability_and_cargo_insurance.presigned_url) {
                await uploadFileS3(values?.liability_and_cargo_insurance_file[0]?.originFileObj as File, invitationCompanyData?.options?.liability_and_cargo_insurance.presigned_url)
                delete values.liability_and_cargo_insurance_file;
                values['liability_and_cargo_insurance'] = invitationCompanyData?.options?.liability_and_cargo_insurance.url || ''
            }
            
            if (!!values?.coverage_options_for_clients_file?.length && invitationCompanyData?.options?.coverage_options_for_clients.presigned_url) {
                await uploadFileS3(values?.coverage_options_for_clients_file[0]?.originFileObj as File, invitationCompanyData?.options?.coverage_options_for_clients.presigned_url)
                delete values.coverage_options_for_clients_file;
                values['coverage_options_for_clients'] = invitationCompanyData?.options?.coverage_options_for_clients.url || ''
            }
            
            await setCompanyDetailsStep(token, values, InvitationStep.STEP_2);
            setFormLoading(false);
            navigate('/auth/login');

        } catch (e: any) {
            const errors = e.response.data.errors;
            if (errors) {
                const fieldErrors = Object.keys(errors).map((field) => ({
                    name: field,
                    errors: errors[field],
                }));
                form.setFields(fieldErrors);
            }
            const message = e.response?.data?.message ? e.response?.data?.message : e.response?.data
            e.response?.data?.message && FlashMessages.error(message);
        }
    };
    
    useEffect(() => {
        form.setFieldValue('certificate_of_insurance', invitationCompanyData?.options?.certificate_of_insurance?.url || '')
    }, [form, invitationCompanyData?.options?.certificate_of_insurance]);

    return (
        <div>
            <div className="auth-register-top auth-register-group">
                <h1 className="heading h2">Tell us more about your company</h1>
            </div>
            <CustomForm
                form={form}
                onSubmit={onFinish}
            >
                <div className="auth-register-group">
                    
                    <RadioGroupField
                        fontSize="medium"
                        className="auth-form-item"
                        name="background_checks"
                        label="Do you provide background checks of your Employees/Staff?"
                        options={[
                            { label: 'Yes', value: 'Yes' },
                            { label: 'No', value: 'No' },
                        ]}
                        rules={[{ required: true, message: 'Please select a answer!' }]}
                    />
                    
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="usdot_number"
                        label="USDOT Number:"
                        rules={[
                            { required: true, message: "USDOT number is required" },
                        ]}
                        placeholder="Placeholder"
                        className="auth-form-item"
                    />
                    
                    <InputField name="certificate_of_insurance" hidden={true} />
                    
                    <RadioGroupField
                        fontSize="medium"
                        className="auth-form-item"
                        name="collect_mvr"
                        label="Do you collect Motor Vehicle Reports (MVR) from drivers/crew leads?"
                        options={[
                            { label: 'Yes', value: 'Yes' },
                            { label: 'No', value: 'No' },
                        ]}
                        rules={[{ required: true, message: 'Please select a answer!' }]}
                    />
                    
                    <RadioGroupField
                        fontSize="medium"
                        className="auth-form-item"
                        name="employee_drug_screen"
                        label="Do employees undergo drug screening?"
                        options={[
                            { label: 'Yes', value: 'Yes' },
                            { label: 'No', value: 'No' },
                        ]}
                        rules={[{ required: true, message: 'Please select a answer!' }]}
                    />
                
                
                </div>
                
                <div className="auth-register-group box">
                    <div className="auth-register-section">
                        <h2 className="heading h3">Warehouse Information</h2>
                        <span>Optional</span>
                    </div>
                    
                    <InputField
                        fontSize="medium"
                        fieldSize="medium"
                        name="warehouse_business_address"
                        label="Business Address:"
                        placeholder="Placeholder"
                    />
                    
                    <Row gutter={24} >
                        <Col className="gutter-row" span={16}>
                            <InputField
                                fontSize="medium"
                                fieldSize="medium"
                                name="warehouse_city"
                                label="City"
                                placeholder="City"
                            />
                        </Col>
                        <Col className="gutter-row" span={8}>
                            <InputField
                                fontSize="medium"
                                fieldSize="medium"
                                name="warehouse_zip"
                                label="ZIP"
                                type="number"
                                inputProps={{
                                    onWheel: (e) => e.currentTarget.blur()
                                }}
                                placeholder="Code"
                            />
                        </Col>
                    </Row>
                    
                    <RadioGroupField
                        fontSize="medium"
                        name="warehouse_contents_trough_inventory"
                        label="Do contents go through inventory?"
                        options={[
                            { label: 'Yes', value: true },
                            { label: 'No', value: false },
                        ]}
                    />
                    
                    <RadioGroupField
                        fontSize="medium"
                        name="warehouse_climate_controlled"
                        label="Climate controlled?"
                        options={[
                            { label: 'Yes', value: true },
                            { label: 'No', value: false },
                        ]}
                    />
                    
                    <SelectField
                        fontSize="medium"
                        label="Methods of storage:"
                        required={true}
                        placeholder="Select storage method"
                        name="warehouse_method_of_storage"
                        selectProps={{
                            size: 'middle',
                            mode: "multiple",
                            allowClear: true
                        }}
                        options={invitationCompanyData?.options?.warehouse_method_of_storage.map((item) => {
                            return ({
                                label: item.label,
                                value: item.value
                            })
                        }) || []}
                    />
                </div>
                
                <div className="auth-register-group">
                    <div className="auth-register-section">
                        <h2 className="heading h3">Please provide documentation/evidence for the following</h2>
                    </div>
                    
                    <FileUpload
                        maxFileSize={10}
                        form={form}
                        className="auth-form-item"
                        name="certificate_of_insurance_file"
                        label="Certificate of Insurance"
                        rules={[{ required: true, message: 'Please upload a file!' }]}
                    />
                    
                    <FileUpload
                        form={form}
                        className="auth-form-item"
                        name="coverage_options_for_clients_file"
                        label="Coverage Options for Clients"
                        maxFileSize={10}
                        optionalLabel={true}
                    />
                    
                    <FileUpload
                        form={form}
                        className="auth-form-item"
                        name="liability_and_cargo_insurance_file"
                        label="Liability and Cargo Insurance"
                        maxFileSize={10}
                        optionalLabel={true}
                    />
                    
                    <InputField
                        name="signature"
                        label="Signature"
                        rules={[
                            { required: true, message: "Signature is required" },
                        ]}
                        placeholder="Placeholder"
                        className="auth-form-item"
                    />
                </div>
                
                
                <div className="auth-register-actions auth-register-group">
                    <Button
                        className="auth-register-actions-link"
                        color="default"
                        icon={<Arrow className="icon-regular" direction="left"/>}
                        iconPosition="start"
                        htmlType="button"
                        onClick={() => onStepChange(InvitationStep.STEP_1)}
                        variant="link"
                        variation={ButtonVariation.LINK_SEMIBOLD}
                    >
                        Back
                    </Button>
                    <Button
                        className="auth-register-actions-button"
                        buttonSize={ButtonSize.LARGE}
                        type="primary"
                        disabled={formLoading}
                    >
                        Complete Registration
                    </Button>
                </div>
            </CustomForm>
        </div>
    );
};

export default CompanySecondary;
