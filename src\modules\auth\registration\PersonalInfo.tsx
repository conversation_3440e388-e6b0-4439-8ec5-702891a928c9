import PasswordField from '../../app/shared/form/PasswordField.tsx';
import { RuleObject } from 'antd/es/form';
import {
    hasEightLetterMin,
    hasLowercaseLetter,
    hasNumber,
    hasSymbol,
    hasUppercaseLetter
} from '../../../utils/helpers.ts';
import PasswordRequirementsList from '../../app/shared/form/PasswordRequirementsList.tsx';
import { Form } from 'antd';
import Button, { ButtonSize, ButtonVariation } from '../../app/shared/Button.tsx';
import CustomForm from '../../app/shared/form/Form.tsx';
import InputField from '../../app/shared/form/InputField.tsx';
import CheckboxField from '../../app/shared/form/CheckboxField.tsx';
import React, { useEffect, useState } from 'react';
import { useInvitationStore } from '../../../store';
import { InvitationStep } from '../../../store/invitation/types/InvitationData.ts';
import { useNavigate } from 'react-router-dom';
import FlashMessages from '../../app/shared/FlashMessages.tsx';

interface Props {
    onStepChange: (step: InvitationStep) => void;
    token: string;
}

const PersonalInfo: React.FC<Props> = ({ onStepChange, token }) => {
    const [form] = Form.useForm();
    const password = Form.useWatch('password', form);
    const {personalData, updatePassword} = useInvitationStore();
    const navigate = useNavigate();
    const [submitting, setSubmitting] = useState<boolean>(false);
    
    const onFinish = async (values: {password: string}) => {
        setSubmitting(true);
        try {
            await form.validateFields();
            await updatePassword(token, values.password)
            onStepChange(InvitationStep.STEP_1)
            
        } catch (e: any) {
            const errors = e.response.data.errors;
            if (errors) {
                const fieldErrors = Object.keys(errors).map((field) => ({
                    name: field,
                    errors: errors[field],
                }));
                form.setFields(fieldErrors);
            }
            const message = e.response?.data?.message ? e.response?.data?.message : e.response?.data
            e.response?.data?.message && FlashMessages.error(message);
        } finally {
            setSubmitting(false);
        }
    };
    
    useEffect(() => {
        form.setFieldValue('name', personalData?.name);
        form.setFieldValue('email', personalData?.email);
    }, [personalData, form]);
    
    return (
        <div>
            <h1 className="heading h2 margin-bottom-10">Tell us more about your company</h1>
            <p className="font-medium font-weight-semibold">
                This is initial information about your company. You can change it anytime.
            </p>
            <CustomForm
                form={form}
                onSubmit={onFinish}
            >
                <InputField
                    fontSize="medium"
                    fieldSize="medium"
                    name="name"
                    label="Company Name"
                    rules={[
                        { required: true, message: "Vendor name is required" },
                    ]}
                    className="auth-form-item"
                    disabled={true}
                />
                
                <InputField
                    fontSize="medium"
                    fieldSize="medium"
                    name="email"
                    label="Email address:"
                    rules={[
                        { required: true, message: "Email address is required" },
                        { type: 'email', message: 'Please enter a valid email address.' }
                    ]}
                    type="email"
                    className="auth-form-item"
                    disabled={true}
                />
                
                <PasswordField
                    fontSize="medium"
                    fieldSize="medium"
                    name="password"
                    placeholder="● ● ● ● ● ● ● ● ●"
                    rules={[
                        { required: true, message: "Password is required" },
                        {
                            validator: (_: RuleObject, value: string) => {
                                if (!value) return Promise.resolve();
                                const conditions = [
                                    hasLowercaseLetter(value),
                                    hasUppercaseLetter(value),
                                    hasNumber(value),
                                    hasSymbol(value),
                                    hasEightLetterMin(value),
                                ];
                                const allConditionsMet = conditions.every(Boolean);
                                
                                if (allConditionsMet) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error("Password does not meet the requirements")
                                );
                            },
                        },
                    ]}
                    label="New Password"
                />
                
                <PasswordRequirementsList inputValue={password}/>
                
                <CheckboxField
                    rules={[
                        { required: true, message: "Terms is required" },
                    ]}
                    name="terms"
                    valuePropName="checked"
                    className="auth-form-item big"
                    label={
                        <>
                            I agree with <Button variation={ButtonVariation.LINK_SEMIBOLD}
                                                 variant="link" color="default">Terms & Conditions</Button>
                        </>
                    }
                />
                
                <Button
                    className="auth-button"
                    buttonSize={ButtonSize.LARGE}
                    disabled={submitting}
                    type="primary"
                >
                    Sign up
                </Button>
                
                <div className="font-regular align-center">
                    Already have an account?
                    <Button
                        variation={ButtonVariation.LINK}
                        variant="link"
                        color="default"
                        onClick={() => navigate('/auth/login')}
                        className="margin-top-16 margin-left-4"
                    >
                        Login
                    </Button>
                </div>
            </CustomForm>
        </div>
    );
};

export default PersonalInfo;
