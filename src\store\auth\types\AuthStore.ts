import { PasswordChange, LoginData, PasswordConfirmation, User } from './AuthData.ts';

export interface AuthStore {
    refreshToken: string | null;
    expiresAt: Date | null;
    _retry: boolean;
    accessToken: string | null;
    user: User | null;
    isAuthenticated: () => boolean;
    
    
    login: (data: LoginData) => Promise<void>;
    unload: () => any;
    logout: () => any;
    resetPassword: (data: PasswordConfirmation) => Promise<void>;
    forgotPassword: (email: string) => Promise<void>;
    changePassword: (data: PasswordChange) => Promise<void>;
    refresh: () => any;
}
