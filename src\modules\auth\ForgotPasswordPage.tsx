import AuthLayout from '../app/layouts/AuthLayout.tsx';
import withoutAuth from '../../hooks/withoutAuth.tsx';
import { Form } from 'antd';
import CustomForm from '../app/shared/form/Form.tsx';
import InputField from '../app/shared/form/InputField.tsx';
import Button, { ButtonVariation } from '../app/shared/Button.tsx';
import { useState } from 'react';
import { useAuthStore } from '../../store';
import { useNavigate } from 'react-router-dom';


const ForgotPasswordPage = () => {
    const [form] = Form.useForm();
    const [emailSent, setEmailSent] = useState<boolean>(false);
    const { forgotPassword } = useAuthStore();
    const [canSendAgain, setCanSendAgain] = useState<boolean>(true);
    const [isLoading, setIsLoading] = useState(false);
    const navigate = useNavigate();
    
    const forgotEmailAction = (email: string) => {
        setIsLoading(true);
        forgotPassword(email).then(() => {
            setCanSendAgain(false);
            setEmailSent(true);
        }).catch((e) => {
            console.error(e);
        }).finally(() => {
            setTimeout(() => {
                setCanSendAgain(true);
            }, 3000)
            setIsLoading(false);
        });
    }
    
    const onFinish = async (values: { email: string }) => {
        forgotEmailAction(values.email);
    };
    
    return (
        <AuthLayout
            className="auth-fp auth-bg"
            subtitle={
                emailSent ?
                    `We sent a reset link to ${form.getFieldValue('email')}`
                    :
                    "Enter your email for instructions"
            }
            title={
                emailSent ?
                    "Check your mail!"
                    :
                    "Forgot Password?"
            }
        >
            {
                !emailSent &&
                <CustomForm
                    form={form}
                    onSubmit={onFinish}
                    className="auth-form"
                >
                    <InputField
                        fieldSize="medium"
                        fontSize="medium"
                        name="email"
                        label="Email address:"
                        rules={[
                            { required: true, message: "Email address is required" },
                            { type: 'email', message: 'Please enter a valid email address.' }
                        ]}
                        type="email"
                        placeholder="Email address"
                        className="auth-form-item big"
                    />

                    <Button
                        className="auth-button"
                        disabled={isLoading}
                        htmlType="submit"
                        type="primary"
                    >
                        Send a reset link
                    </Button>

                    <Button
                        variation={ButtonVariation.LINK_SEMIBOLD}
                        htmlType="button"
                        onClick={() => navigate('/auth/login')}
                        variant="link"
                        color="default"
                        type="primary"
                        className="auth-button-back"
                    >
                        Back
                    </Button>
                </CustomForm>
            }
            {
                emailSent &&
                <div className="auth-fp-bottom">
                    Didn’t receive an email?
                    {' '}
                    <Button
                        variant="link"
                        color="default"
                        onClick={() => canSendAgain && forgotEmailAction(form.getFieldValue('email'))}
                        variation={ButtonVariation.LINK}
                        disabled={!canSendAgain}
                    >
                        Resend
                    </Button>
                    <Button
                        variation={ButtonVariation.LINK_SEMIBOLD}
                        htmlType="button"
                        onClick={() => navigate('/auth/login')}
                        variant="link"
                        color="default"
                        type="primary"
                        className="auth-button-back"
                    >
                        Back
                    </Button>
                </div>
            }
        </AuthLayout>
    );
};

export default withoutAuth(ForgotPasswordPage);
