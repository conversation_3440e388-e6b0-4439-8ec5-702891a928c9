import React from 'react';
import { Collapse, type CollapseProps } from 'antd';
import Chevron from './svg/Chevron.tsx';

interface Props extends  CollapseProps {
    className?: string;
}

const Accordion: React.FC<Props> = ({ className, ...restProps }) => {
    return (
        <Collapse
            prefixCls="accordion"
            className={className}
            expandIconPosition="end"
            expandIcon={(open) => {
                return (
                    <Chevron direction={open.isActive ? "up" : "down"} />
                )
            }}
            {...restProps}
        />
    );
}
export default Accordion;
