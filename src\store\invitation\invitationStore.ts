import { InvitationStore } from './types/InvitationStore.ts';
import { create } from 'zustand/react';
import { publicApi } from '../../common/api.ts';
import { InvitationCompanyData, InvitationStep } from './types/InvitationData.ts';
import { DEFAULT_PER_PAGE } from '../../@types';

export const invitationStore = create<InvitationStore> ((set, get) => ({
    personalData: null,
    invitationCompanyData: null,
    
    checkToken: async (token) => {
        const response = await publicApi.get(`/invitation/${token}`);
        set({personalData: response.data.data});
        return response.data.data;
    },
    
    updatePassword: async (token, password) => {
        const response = await publicApi.post(`/invitation/${token}`, {
            password: password
        });
        set({personalData: response.data.data})
        return response.data.data;
    },
    
    getCompanyData: async (token) => {
        const response = await publicApi.get(`/invitation/${token}/data`);
        set({invitationCompanyData: response.data.data})
        return response.data.data;
    },
    
    setCompanyDetailsStep: async (token, data, step) => {
        let endpoint = '';
        if (step === InvitationStep.STEP_1) {
            endpoint = `/invitation/${token}/step-1`;
        }
        if (step === InvitationStep.STEP_2) {
            endpoint = `/invitation/${token}/step-2`;
        }
        
        const response = await publicApi.post(endpoint, data);
        set({personalData: response.data.data});
        
        const currentCompanyData = get().invitationCompanyData;
        
        const updatedCompanyData: InvitationCompanyData = {
            ...currentCompanyData,
            form: {
                ...currentCompanyData?.form,
                ...data
            }
        };
        
        set({
            invitationCompanyData: updatedCompanyData,
            personalData: response.data.data
        });
    },
    
    getBusinessContacts: async (token, payload) => {
        const limit = payload?.limit ? payload.limit : DEFAULT_PER_PAGE;
        const response = await publicApi.get(`/invitation/${token}/contacts`, {
            params: {...payload, limit: limit}
        });
        
        return response.data;
    },
}))
