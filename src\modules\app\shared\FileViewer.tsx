import React, { useEffect, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import Button from './Button.tsx';
import 'react-pdf/dist/Page/TextLayer.css';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import Chevron from './svg/Chevron.tsx';
import { isOldSafari } from '../../../utils/isOldSafari.ts';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/build/pdf.worker.min.mjs',
    import.meta.url,
).toString();

interface Props {
    file: File | string;
}

const getFileName = (file: File | string) => {
    if (typeof file === 'string') {
        const parts = file.split('--');
        return parts[parts.length - 1];
    }
    
    return file.name;
}

const getFileExtension = (file: File | string) => {
    if (typeof file === 'string') {
        const parts = file.split('.');
        return parts[parts.length - 1];
    }
    
    const parts = file.type.split('/');
    return parts[parts.length - 1];
}

const FileViewer: React.FC<Props> = ({ file }) => {
    const [numPages, setNumPages] = useState<number>();
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [image, setImage] = useState<string>();
    
    function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
        setNumPages(numPages);
    }
    
    const goToPreviousPage = () => {
        setPageNumber((prevPageNumber) => Math.max(prevPageNumber - 1, 1));
    };
    
    const goToNextPage = () => {
        setPageNumber((prevPageNumber) => Math.min(prevPageNumber + 1, numPages || 1));
    };
    
    useEffect(() => {
        setImage(undefined);
        setNumPages(undefined);
        setPageNumber(1);
    }, [file]);
    
    useEffect(() => {
        if (typeof file === 'object' && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = () => {
                setImage(reader.result as string);
            };
            reader.readAsDataURL(file);
            
            return;
        }
        
        if (typeof file === 'string') {
            if (file.endsWith('.png') || file.endsWith('.jpeg') || file.endsWith('.jpg') ) {
                setImage(file);
                return;
            }
        }
    }, [file]);
    
    useEffect(() => {
        return () => {
            setImage(undefined);
        }
    }, []);
    
    return (
        <div className="fv-container">
            {image && <img className="fv-image" src={image} alt="alt"/>}
            
            {
                !image &&
                    <Document file={file} className="fv-content" onLoadSuccess={onDocumentLoadSuccess}>
                        <Page pageNumber={pageNumber} width={400}/>
                    </Document>
            }
            
            { isOldSafari() && getFileExtension(file) === 'pdf' && <p className="color-error font-medium">Update Safari to view uploaded PDF document.</p> }
            
            {
                !image && (numPages && numPages > 1) &&
                    <div className="fv-navigation">
                        <Button
                            className="fv-navigation-button prev"
                            onClick={goToPreviousPage}
                            variant="link"
                            color="primary"
                            disabled={pageNumber <= 1}
                            icon={<Chevron direction="left" />}
                        />
                        <Button
                            className="fv-navigation-button next"
                            onClick={goToNextPage}
                            variant="link"
                            color="primary"
                            disabled={pageNumber >= (numPages || 0)}
                            icon={<Chevron direction="right" />}
                        />
                    </div>
            }
            <div className="fv-bottom">
                <div className="fv-container-bottom-title">{getFileName(file)}</div>
                
                {
                    !image && (numPages && numPages > 1) &&
                        <div className="fv-container-bottom-title-meta">
                            Page {pageNumber} of {numPages}
                        </div>
                }
            </div>
        </div>
    );
}
export default FileViewer;
