.sidebar {
    @include flex(flex, column, nowrap, flex-start, stretch);
    background-color: $color-surface;
    border-right: 1px solid $color-border;
    box-shadow: 2px 0 16px 0 rgba(16, 38, 100, 0.04);
    flex-shrink: 0;
    height: 100vh;
    overflow: auto;
    position: relative;
    width: 280px;
    border-radius: 0 $radius-lg $radius-lg 0;

    @include breakpoint(md) {
        position: fixed;
        right: 0;
        top: 0;
        transform: translateX(100%);
        transition: all .3s ease;
        width: 100%;
        z-index: 8;
        border-radius: 0;
    }

    &-logo {
        background-color: $color-surface;
        cursor: pointer;
        padding: $spacing-lg $spacing-xl $spacing-md;
        position: sticky;
        text-align: center;
        top: 0;
        z-index: 2;
        border-bottom: 1px solid $color-border;

        @include breakpoint(md) {
            margin-bottom: $spacing-md;
            padding: $spacing-lg $spacing-xl $spacing-lg $spacing-xl;
        }

        &-image {
            display: block;
            max-width: 120px;
            margin: 0 auto;
        }
    }

    &-content {
        flex: 1;
        margin: 0 $spacing-md;
        padding-top: $spacing-lg;
    }

    &-bottom {
        @include flex(flex, column, nowrap, flex-end, flex-stretch);
        gap: $spacing-lg;
        margin: $spacing-lg $spacing-md 0;
        padding: $spacing-lg $spacing-md;
        border-top: 1px solid $color-border;
        background: $color-background;
        border-radius: $radius-lg;
        box-shadow: 0 2px 8px 0 rgba(16, 38, 100, 0.04);

        &-group {
            @include flex(flex, row, nowrap, space-between, flex-start);
            @include link-color($color-text, $color-text, none, none);
            border-top: none;
            flex: 1;
            gap: $spacing-md;
            padding-top: 0;
            word-break: break-word;
            align-items: center;

            &-meta {
                .title {
                    font-size: $font-size-sm;
                    font-weight: $font-weight-bold;
                    margin-bottom: 2px;
                }

                .label {
                    font-size: $font-size-sm;
                    color: $color-text-light;
                }
            }
        }
    }
}
