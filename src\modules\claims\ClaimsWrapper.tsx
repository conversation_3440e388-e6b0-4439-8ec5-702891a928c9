import withAuth from '../../hooks/withAuth.tsx';
import { Outlet, useOutletContext, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { Claim, ClaimStatus } from '../../store/claims/types/Claim.ts';

export interface ClaimsContextType {
    type: string;
    currentClaim?: Claim;
    setCurrentClaim: (claim?: Claim) => void;
}

export function useClaimsContext() {
    return useOutletContext<ClaimsContextType>();
}

export const getClaimStatusFromParam = (type: string) => {
    switch (type) {
        case 'new':
            return ClaimStatus.NEW
        case 'pending':
            return ClaimStatus.PENDING
        case 'scheduling':
            return ClaimStatus.SCHEDULING
        case 'storage':
            return ClaimStatus.STORAGE
        case 'closed':
            return ClaimStatus.CLOSED
        case 'closed-lost':
            return ClaimStatus.CLOSED_LOST
    }
}

const ClaimsWrapper = () => {
    const params = useParams();
    const [type, setType] = useState<string>('');
    const [currentClaim, setCurrentClaim] = useState<Claim>();
    
    const handleClaimSet = (claim?: Claim) => {
        setCurrentClaim(claim);
    }
    
    useEffect(() => {
        if (!params.type) {
            return;
        }
        setType(params.type);
    }, [params]);
    
    return (
        <Outlet
            context={
                {
                    type,
                    currentClaim,
                    setCurrentClaim: handleClaimSet
                } satisfies ClaimsContextType
            }
        />
    );
};

export default withAuth(ClaimsWrapper);
