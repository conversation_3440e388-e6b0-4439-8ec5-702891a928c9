import { ClaimStatus } from '../store/claims/types/Claim.ts';

export const getStatusClass = (status: ClaimStatus) => {
    if (status === ClaimStatus.NEW) {
        return 'new';
    }
    if (status === ClaimStatus.PENDING) {
        return 'pending';
    }
    if (status === ClaimStatus.STORAGE) {
        return 'storage';
    }
    if (status === ClaimStatus.CLOSED) {
        return 'closed';
    }
    if (status === ClaimStatus.CLOSED_LOST) {
        return 'closed-lost';
    }
    if (status === ClaimStatus.SCHEDULING) {
        return 'scheduling';
    }
    return '';
}
