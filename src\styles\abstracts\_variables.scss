// Font
$font-family: 'Inter', '<PERSON><PERSON><PERSON>s', Aria<PERSON>, sans-serif;
$font-size: 16px;
$font-size-sm: 14px;
$font-size-lg: 20px;
$font-weight-thin: 100;
$font-weight-extrathin: 200;
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;
$line-height: 1.5;
$letter-spacing: -0.01em;

// Colors (Figma-inspired modern palette)
$primary-50: #F0F6FF;
$primary-100: #D6E4FF;
$primary-200: #ADC8FF;
$primary-300: #84A9FF;
$primary-400: #6690FF;
$primary-500: #3366FF;
$primary-600: #254EDB;
$primary-700: #1939B7;
$primary-800: #102693;
$primary-900: #091A7A;

$gray-50: #F9FAFB;
$gray-100: #F3F4F6;
$gray-200: #E5E7EB;
$gray-300: #D1D5DB;
$gray-400: #9CA3AF;
$gray-500: #6B7280;
$gray-600: #4B5563;
$gray-700: #374151;
$gray-800: #1F2937;
$gray-900: #111827;

$accent-50: #FFF7ED;
$accent-100: #FFEDD5;
$accent-200: #FED7AA;
$accent-300: #FDBA74;
$accent-400: #FB923C;
$accent-500: #F97316;
$accent-600: #EA580C;
$accent-700: #C2410C;
$accent-800: #9A3412;
$accent-900: #7C2D12;

$color-primary: $primary-500;
$color-primary-dark: $primary-700;
$color-secondary: $accent-500;
$color-secondary-dark: $accent-700;
$color-background: $gray-50;
$color-background-light: #fff;
$color-surface: $gray-100;
$color-border: $gray-200;
$color-text: $gray-900;
$color-text-light: $gray-500;
$color-link-default: $primary-500;
$color-link-hover: $primary-700;
$color-hr: $gray-200;
$color-heading: $gray-900;
$color-line: $gray-200;

$color-error: #EF4444;
$color-warning: #F59E42;
$color-info: #3B82F6;
$color-success: #22C55E;

// Status colors (can be customized per Figma)
$color-status-new: #489CFB;
$color-status-pending: #F89101;
$color-status-scheduling: #7F31F6;
$color-status-storage: #45DB77;

// Border radius
$radius-sm: 4px;
$radius-md: 8px;
$radius-lg: 16px;
$radius-xl: 24px;

// Spacing
$margin: 24px;
$padding: 24px;
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// Margin & padding loop
$values: 0 4 8 16 24 32;
$positions: top right bottom left;

// Icons path
$icon-path: '/assets/icons/';

// Image path
$image-path: '/assets/';

// Retina image suffix
$image-suffix-2x: '-2x';
$image-suffix-3x: '-3x';

// Font path
$font-path: '../fonts';

// Layout
$content-offset: 56px;

// Grid
$grid-columns: 12;
$grid-column-gutter: 32px;

// break points
$xs: 575px;
$sm: 767px;
$md: 991px;
$lg: 1199px;
