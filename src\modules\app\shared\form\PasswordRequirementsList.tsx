import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { ReactComponent as IconCheck } from '../../../../assets/icons/icon-check-sign.svg';
import {
    hasEightLetterMin,
    hasLowercaseLetter,
    hasNumber,
    hasSymbol,
    hasUppercaseLetter
} from '../../../../utils/helpers.ts';

interface PasswordConditions {
    hasLowercaseLetter: boolean;
    hasUppercaseLetter: boolean;
    hasNumber: boolean;
    hasSymbol: boolean;
    hasEightLetterMin: boolean;
}

const initialState: PasswordConditions = {
    hasLowercaseLetter: false,
    hasUppercaseLetter: false,
    hasNumber: false,
    hasSymbol: false,
    hasEightLetterMin: false,
};


interface Props {
    inputValue: string
}

const PasswordRequirementsList: React.FC<Props> = ({ inputValue = '' }) => {
    const [conditions, setConditions] = useState<PasswordConditions>(initialState);
    
    const checkConditions = (input: string) => {
        const checkerObject: PasswordConditions = {
            hasUppercaseLetter: hasUppercaseLetter(input),
            hasLowercaseLetter: hasLowercaseLetter(input),
            hasNumber: hasNumber(input),
            hasSymbol: hasSymbol(input),
            hasEightLetterMin: hasEightLetterMin(input),
        };
        
        setConditions(checkerObject);
    };
    
    useEffect(() => {
        checkConditions(inputValue);
    }, [inputValue]);
    
    return (
        <div className="auth-list">
            <div
                className={classNames({
                    'auth-list-item': true,
                    'item-active': conditions.hasLowercaseLetter,
                })}
            >
                <IconCheck/>
                One lowercase character
            </div>
            <div
                className={classNames({
                    'auth-list-item': true,
                    'item-active': conditions.hasUppercaseLetter,
                })}
            >
                <IconCheck/>
                One uppercase character
            </div>
            <div
                className={classNames({
                    'auth-list-item': true,
                    'item-active': conditions.hasNumber,
                })}
            >
                <IconCheck/>
                One number
            </div>
            <div
                className={classNames({
                    'auth-list-item': true,
                    'item-active': conditions.hasSymbol,
                })}
            >
                <IconCheck/>
                One special character
            </div>
            <div
                className={classNames({
                    'auth-list-item': true,
                    'item-active': conditions.hasEightLetterMin,
                })}
            >
                <IconCheck/>8 character minimum
            </div>
        </div>
    );
};

export default PasswordRequirementsList;
