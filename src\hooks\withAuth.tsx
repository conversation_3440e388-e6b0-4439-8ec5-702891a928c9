import { FunctionComponent } from 'react';
import withAuthRedirect from './withAuthRedirect';

/**
 * Require the user to be authenticated in order to render the component.
 * If the user isn't authenticated, forward to the given URL.
 */
export default function withAuth(
    WrappedComponent: FunctionComponent,
    location = '/auth/login',
): FunctionComponent {
    return withAuthRedirect({
        WrappedComponent,
        location,
        expectedAuth: true,
    });
}
