<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ADD Button Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.pass {
            background-color: #d4edda;
            color: #155724;
        }
        .status.fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            margin-right: 8px;
        }
        .checklist li.checked:before {
            content: "☑ ";
            color: green;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CreateEstimateModal ADD Button Test</h1>
        
        <div class="instructions">
            <h3>Test Instructions</h3>
            <p>Follow these steps to test the ADD button functionality in the CreateEstimateModal:</p>
            <ol>
                <li>Open the application at <a href="http://127.0.0.1:3601/" target="_blank">http://127.0.0.1:3601/</a></li>
                <li>Navigate to a claim page</li>
                <li>Click on "Create Estimate" or similar button to open the CreateEstimateModal</li>
                <li>Select a service category tab (e.g., Packing, Storage, etc.)</li>
                <li>Fill in the form fields:
                    <ul>
                        <li>Select a service from the dropdown</li>
                        <li>Enter a quantity (number)</li>
                        <li>Enter a rate (number)</li>
                        <li>If Storage category, also enter length</li>
                    </ul>
                </li>
                <li>Click the "ADD" button</li>
                <li>Check if the item appears in the table below</li>
                <li>Open browser console (F12) to see debug logs</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Test Checklist</h3>
            <ul class="checklist">
                <li id="test1">Modal opens successfully</li>
                <li id="test2">Service dropdown populates with options</li>
                <li id="test3">Form fields accept input (quantity, rate, length for Storage)</li>
                <li id="test4">ADD button becomes enabled when all required fields are filled</li>
                <li id="test5">Clicking ADD button adds item to the table</li>
                <li id="test6">Added item displays correct service name</li>
                <li id="test7">Added item displays correct quantity</li>
                <li id="test8">Added item displays correct rate</li>
                <li id="test9">Added item displays correct total (quantity × rate)</li>
                <li id="test10">Form fields clear after adding item</li>
                <li id="test11">Can add multiple items to the same category</li>
                <li id="test12">Remove button (minus icon) works for added items</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Expected Console Logs</h3>
            <p>When testing, you should see these console logs:</p>
            <ul>
                <li><code>Select option changed: [selected_value]</code></li>
                <li><code>Form values: {selectValue, quantityValue, rateValue, groupName}</code></li>
                <li><code>Found item: [item_object]</code></li>
                <li><code>Adding item: [new_item_object]</code></li>
                <li><code>Current form values before add: [form_values]</code></li>
                <li><code>Current form values after add: [updated_form_values]</code></li>
                <li><code>Fields array: [fields_array]</code></li>
                <li><code>Processing field: [field_object] index: [index]</code></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Common Issues to Check</h3>
            <ul>
                <li><strong>ADD button disabled:</strong> Ensure all required fields (service, quantity, rate) are filled</li>
                <li><strong>Item not appearing:</strong> Check console for errors, verify form field names match</li>
                <li><strong>Incorrect data display:</strong> Check if field values are being retrieved correctly</li>
                <li><strong>Form not clearing:</strong> Verify setFieldsValue is called with correct field names</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <p>Status: <span class="status pending" id="overall-status">PENDING</span></p>
            <p id="test-notes">Click the checkboxes above as you complete each test step.</p>
        </div>
    </div>

    <script>
        // Simple test tracking
        const checkboxes = document.querySelectorAll('.checklist li');
        const overallStatus = document.getElementById('overall-status');
        const testNotes = document.getElementById('test-notes');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('click', function() {
                this.classList.toggle('checked');
                updateOverallStatus();
            });
        });

        function updateOverallStatus() {
            const total = checkboxes.length;
            const checked = document.querySelectorAll('.checklist li.checked').length;
            
            if (checked === 0) {
                overallStatus.textContent = 'PENDING';
                overallStatus.className = 'status pending';
                testNotes.textContent = 'Click the checkboxes above as you complete each test step.';
            } else if (checked === total) {
                overallStatus.textContent = 'PASS';
                overallStatus.className = 'status pass';
                testNotes.textContent = 'All tests completed successfully!';
            } else {
                overallStatus.textContent = `IN PROGRESS (${checked}/${total})`;
                overallStatus.className = 'status pending';
                testNotes.textContent = `${checked} of ${total} tests completed.`;
            }
        }
    </script>
</body>
</html>
