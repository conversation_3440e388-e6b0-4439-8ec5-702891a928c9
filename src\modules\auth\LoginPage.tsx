import AuthLayout from '../app/layouts/AuthLayout.tsx';
import withoutAuth from '../../hooks/withoutAuth.tsx';
import { Form } from 'antd';
import CustomForm from '../app/shared/form/Form.tsx';
import InputField from '../app/shared/form/InputField.tsx';
import PasswordField from '../app/shared/form/PasswordField.tsx';
import Button, { ButtonVariation } from '../app/shared/Button.tsx';
import { useAuthStore } from '../../store';
import { LoginData } from '../../store/auth/types/AuthData.ts';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import FlashMessages from '../app/shared/FlashMessages.tsx';
import CheckboxField from '../app/shared/form/CheckboxField.tsx';


const LoginPage = () => {
    const [form] = Form.useForm();
    const { login } = useAuthStore();
    const navigate = useNavigate()
    const [submitting, setSubmitting] = useState<boolean>(false);
    
    const onFinish = (values: LoginData) => {
        setSubmitting(true);
        login(values).then(() => {
            navigate('/');
        }).catch(() => {
            FlashMessages.error('The email or password is incorrect.');
            form.setFields([
                {
                    name: 'email',
                    errors: ['']
                },
                {
                    name: 'password',
                    errors: ['']
                }
            ])
        }).finally(() => {
            setSubmitting(false);
        })
    };
    
    return (
        <AuthLayout
            className="auth-login auth-bg"
            subtitle="Please enter your email and password to continue"
            title="Login to Account"
        >
            <CustomForm
                form={form}
                onSubmit={onFinish}
                className="auth-form"
            >
                <InputField
                    fieldSize="medium"
                    fontSize="medium"
                    name="email"
                    label="Email address:"
                    rules={[
                        { required: true, message: "Email address is required" },
                        { type: 'email', message: 'Please enter a valid email address.' }
                    ]}
                    type="email"
                    placeholder="Email address"
                    className="auth-form-item"
                />
                
                <div className="auth-form-item">
                    <div className="auth-form-item-helper">
                        <label htmlFor="password">Password</label>
                        <Button
                            variation={ButtonVariation.LINK}
                            htmlType="button"
                            variant="link"
                            color="default"
                            onClick={() => navigate('/auth/forgot-password')}
                        >
                            Forgot Password?
                        </Button>
                    </div>
                    
                    <PasswordField
                        fieldSize="medium"
                        fontSize="medium"
                        name="password"
                        placeholder="● ● ● ● ● ● ● ● ●"
                        rules={[{ required: true, message: "Password is required" }]}
                    />
                    
                    <CheckboxField
                        name="remember_me"
                        valuePropName="checked"
                        // className="auth-form-item"
                        label="Remember me"
                        fontSize="medium"
                    />
                </div>
                
                <Button className="auth-button" disabled={submitting}
                        type="primary">Submit</Button>
            </CustomForm>
        </AuthLayout>
    );
};

export default withoutAuth(LoginPage);
