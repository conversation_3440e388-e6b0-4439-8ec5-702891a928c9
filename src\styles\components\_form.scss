.form {
    input {
        border: 1px solid $color-border;
        border-radius: $radius-md;
        font-size: $font-size-sm;
        letter-spacing: $letter-spacing;
        line-height: 1.5;
        padding: $spacing-md $spacing-lg;
        background: $color-background-light;
        color: $color-text;
        transition: border 0.2s, box-shadow 0.2s;
        &:focus {
            border-color: $color-primary;
            box-shadow: 0 0 0 2px rgba($color-primary, 0.08);
            outline: none;
        }
        &:disabled {
            color: $color-text-light;
            background: $gray-100;
        }
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        [type=number] {
            -moz-appearance: textfield;
        }
    }
    &-field {
        margin-bottom: $spacing-lg;
        label {
            font-size: $font-size-sm !important;
            font-weight: $font-weight-semibold;
            color: $color-heading;
            margin-bottom: $spacing-xs;
            .info {
                color: $color-text-light;
                margin-left: $spacing-sm;
            }
        }
        &.medium {
            input {
                font-size: $font-size;
            }
            label {
                font-size: $font-size !important;
            }
        }
        &-input {
            border: 1px solid $color-border;
            border-radius: $radius-md;
            font-size: $font-size-sm;
            line-height: 1.5;
            letter-spacing: $letter-spacing;
            padding: $spacing-md $spacing-lg;
            background: $color-background-light;
            color: $color-text;
            transition: border 0.2s, box-shadow 0.2s;
            &:focus {
                border-color: $color-primary;
                box-shadow: 0 0 0 2px rgba($color-primary, 0.08);
                outline: none;
            }
            &.medium {
                padding: $spacing-lg $spacing-lg;
            }
        }
        &-select {
            height: auto !important;
            width: 100% !important;
            &.medium {
                .ant-select {
                    &-selection {
                        &-item {
                            font-size: $font-size !important;
                        }
                        &-placeholder {
                            font-size: $font-size !important;
                        }
                    }
                }
                .ant-select-item-option {
                    font-size: $font-size;
                }
            }
            .ant-select {
                &-selector {
                    padding: $spacing-md $spacing-lg !important;
                    border-radius: $radius-md !important;
                    border: 1px solid $color-border !important;
                    background: $color-background-light !important;
                }
                &-selection {
                    &-item {
                        font-size: $font-size-sm !important;
                    }
                    &-placeholder {
                        font-size: $font-size-sm !important;
                    }
                    &-search {
                        inset-inline: initial !important;
                    }
                }
            }
            input {
                height: 100% !important;
            }
            &-popup {
                .ant-select-item-option {
                    font-size: $font-size-sm;
                }
            }
        }
        &-radio {
            &-wrapper {
                @include flex(flex, row, nowrap, flex-start, center);
                gap: $spacing-lg;
                label {
                    margin-right: 0;
                }
            }
        }
        &-upload {
            &-dragger {
                color: $color-primary;
                &-inner {
                    background: transparent !important;
                }
                &-content {
                    @include flex(flex, row, nowrap, space-between, stretch);
                    gap: $spacing-lg;
                    &-body {
                        @include flex(flex, column, nowrap, space-between, flex-start);
                        gap: $spacing-sm;
                        &-title {
                            font-size: $font-size-sm;
                            span {
                                font-weight: $font-weight-semibold;
                                text-decoration: underline;
                            }
                        }
                        &-label {
                            color: $color-text-light;
                            font-size: 12px;
                        }
                    }
                }
            }
            &-error {
                color: $color-error;
                margin-bottom: $spacing-lg;
                margin-top: -$spacing-lg;
                padding-left: $spacing-md;
            }
            &-progress {
                margin-top: $spacing-lg;
                &-inner {
                    @include flex(flex, row, wrap, flex-start, flex-start);
                    gap: $spacing-md;
                    padding: $spacing-md;
                }
                &-label {
                    margin-bottom: $spacing-sm;
                }
                &-icon {
                    flex-shrink: 0;
                    opacity: .4;
                }
                &-content {
                    flex: 1;
                    &-top {
                        @include flex(flex, row, nowrap, space-between, flex-start);
                        gap: $spacing-md;
                        margin-bottom: $spacing-sm;
                        .highlight {
                            font-size: 12px;
                        }
                        .label {
                            @include flex(flex, row, nowrap, space-between, flex-start);
                            color: $color-text-light;
                            flex-shrink: 0;
                            font-size: 10px;
                        }
                    }
                    &-bar {
                        @include flex(flex, row, wrap, flex-start, flex-start);
                        .ant-progress {
                            &-inner {
                                height: 3px;
                            }
                            &-bg {
                                background-color: $color-primary;
                                height: 3px !important;
                            }
                        }
                    }
                    &-remove {
                        cursor: pointer;
                        font-size: 24px;
                        margin-top: -5px;
                    }
                }
            }
        }
        &-checkbox {
            &.medium {
                font-size: $font-size !important;
            }
        }
    }
    .ant-input-number {
        width: initial;
    }
    .form-error, .ant-form-item-explain-error {
        color: $color-error;
        font-size: $font-size-sm;
        margin-top: $spacing-xs;
        margin-bottom: $spacing-xs;
    }
}

.claim-ce-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed $color-border;
    border-radius: $radius-lg;
    padding: 40px 16px 32px 16px;
    background: $color-background-light;
    margin-bottom: 24px;
    min-height: 220px;
    text-align: center;
    position: relative;

    &-icon {
        color: $color-secondary;
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
    }
    &-description {
        margin-bottom: 20px;
        &-title {
            font-size: 16px;
            font-weight: $font-weight-regular;
            color: $color-text;
            margin-bottom: 4px;
        }
        &-label {
            font-size: 13px;
            color: $color-text-light;
        }
    }
    .button {
        border: 1.5px solid $color-secondary;
        color: $color-secondary;
        background: transparent;
        border-radius: $radius-md;
        font-weight: $font-weight-bold;
        font-size: 15px;
        padding: 10px 28px;
        margin-bottom: 0;
        margin-top: 8px;
        box-shadow: none;
        &:hover, &:focus {
            background: $color-secondary;
            color: #fff;
        }
    }
}

// Modal footer button (Next)
.claim-ce-modal-footer {
    .button {
        width: 100%;
        background: $color-text;
        color: #fff;
        border-radius: $radius-md;
        font-size: 17px;
        font-weight: $font-weight-bold;
        padding: 14px 0;
        margin-top: 0;
        border: none;
        &:hover, &:focus {
            background: darken($color-text, 10%);
            color: #fff;
        }
    }
}
