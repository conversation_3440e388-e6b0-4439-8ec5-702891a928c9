import { EstimateCreateProduct, EstimateItem } from '../../../../store/claims/types/Claim.ts';

export const calculateTotalUnitPrice = (data: Record<string, EstimateCreateProduct[] | EstimateItem[]>): number => {
    return Object.values(data)
        .flat()
        .reduce((sum, item) => sum + parseFloat(String(item.unit_price * item.quantity)), 0);
};

export const PriceBookSortingGroups = ['Packing', 'Pack Out', 'Storage', 'Pack Back', 'Unpacking']
