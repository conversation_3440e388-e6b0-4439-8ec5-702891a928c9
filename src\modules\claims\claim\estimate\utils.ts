import { EstimateCreateProduct, EstimateItem } from '../../../../store/claims/types/Claim.ts';

export const calculateTotalUnitPrice = (data: Record<string, EstimateCreateProduct[] | EstimateItem[]>): number => {
    return Object.values(data)
        .flat()
        .reduce((sum, item) => {
            const baseTotal = item.unit_price * item.quantity;
            // For Storage items, multiply by length if it exists
            if (item.product_family === 'Storage' && (item as any).length) {
                return sum + parseFloat(String(baseTotal * (item as any).length));
            }
            return sum + parseFloat(String(baseTotal));
        }, 0);
};

export const PriceBookSortingGroups = ['Packing', 'Pack Out', 'Storage', 'Pack Back', 'Unpacking']
