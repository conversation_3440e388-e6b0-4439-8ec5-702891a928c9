import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { ReactComponent as IconClose } from '../../../assets/icons/icon-close.svg';
import Button, { ButtonVariation } from './Button';

export enum ModalSize {
    DEFAULT = 'default',
    MEDIUM = 'medium',
    BIG = 'big',
    LARGE = 'large'
}

interface Props {
    children: React.ReactNode;
    className?: string;
    contentClassName?: string;
    description?: React.ReactNode;
    disableCloseIcon?: boolean;
    onClose: () => void;
    position?: 'left' | 'right' | 'center';
    show: boolean;
    sideContent?: { content?: React.ReactNode, title?: string, description?: string, className?: string };
    size?: ModalSize;
    title?: string;
}

const Modal: React.FC<Props> = ({
    children,
    className,
    contentClassName,
    disableCloseIcon = false,
    description,
    onClose,
    position = 'right',
    show,
    size = ModalSize.DEFAULT,
    sideContent,
    title,
}) => {
    const [showModal, setShowModal] = useState<boolean>(false);
    
    const handleKeydown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            onClose();
        }
    };
    
    useEffect(() => {
        document.addEventListener('keydown', handleKeydown);
        return () => {
            document.removeEventListener('keydown', handleKeydown);
        };
    }, []); // eslint-disable-line react-hooks/exhaustive-deps
    
    useEffect(() => {
        setTimeout(() => {
            setShowModal(show);
        }, 1);
    }, [show]);
    
    if (!show) {
        return;
    }
    
    return ReactDOM.createPortal(
        <div
            className={classNames(
                {
                    modal: true,
                    show: showModal,
                },
                className,
                position,
            )}
        >
            <div onClick={onClose} className="modal-overlay"></div>
            <div
                className={classNames("modal-content", size)}
                onClick={e => e.stopPropagation()}
            >
                {
                    sideContent && !!Object.keys(sideContent).length &&
                    <div className="modal-content-side">
                        {(sideContent.title || sideContent.description) &&
                            <div className="modal-content-side-head">
                                <div
                                    className="modal-content-side-head-content"
                                >
                                    {sideContent.title &&
                                        <h5 className="modal-content-side-head-title">{sideContent.title}</h5>}
                                    
                                    {sideContent.description &&
                                        <p className="modal-content-side-head-description">{sideContent.description}</p>}

                                </div>
                            </div>
                        }
                        <div
                            className={classNames("modal-content-side-body", {
                                offset: !(sideContent.title || sideContent.description)
                            })}
                        >
                            {sideContent.content}
                        </div>
                    </div>
                }
                
                <div className={classNames("modal-content-inner", contentClassName)}>
                    <div className="modal-content-head">
                        {!disableCloseIcon && (
                            <Button
                                className="modal-content-head-close"
                                variation={ButtonVariation.LINK}
                                variant="link"
                                color="default"
                                onClick={onClose}
                                icon={<IconClose className="icon-regular"/>}
                            />
                        )}
                        {
                            (title || description) &&
                            <div
                                className="modal-content-head-content"
                            >
                                {title && <h5 className="modal-content-head-title">{title}</h5>}
                                
                                {description && <p className="modal-content-head-description">{description}</p>}

                            </div>
                        }
                    </div>
                    
                    <div className="modal-content-body">{children}</div>
                </div>
            </div>
        </div>,
        document.body,
    );
};

export default Modal;
