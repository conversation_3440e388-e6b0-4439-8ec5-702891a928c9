import React from 'react';

interface Props {
    className?: string;
    direction?: 'left' | 'right' | 'up' | 'down';
}

const Degrees = {
    left: 0,
    right: 180,
    up: 90,
    down: 270,
};

const Arrow: React.FC<Props> = ({ className, direction = 'right' }) => {
    const deg = Degrees[direction];
    
    return (
        <svg
            className={className}
            style={{
                transform: `rotate(${deg}deg)`,
            }}
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M6 12h12M9.6 15.6 6 12l3.6-3.6"
            />
        </svg>
    );
};

export default Arrow;
