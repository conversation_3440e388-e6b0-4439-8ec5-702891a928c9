.button {
    border-radius: $radius-md;
    font-size: $font-size;
    font-weight: $font-weight-semibold;
    line-height: 1.5;
    height: auto;
    padding: $spacing-md $spacing-lg;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    white-space: normal;
    border: none;
    box-shadow: 0 1px 2px 0 rgba(16,38,100,0.03);

    &.primary {
        background: $color-primary;
        color: #fff;
        &:hover, &:focus {
            background: $color-primary-dark;
            color: #fff;
            box-shadow: 0 2px 8px 0 rgba(16,38,100,0.08);
        }
        &:active {
            background: $color-primary-dark;
            color: #fff;
        }
        &:disabled, &.disabled {
            background: $gray-200;
            color: $gray-400;
            cursor: not-allowed;
        }
    }
    &.secondary {
        background: $color-secondary;
        color: #fff;
        &:hover, &:focus {
            background: $color-secondary-dark;
            color: #fff;
            box-shadow: 0 2px 8px 0 rgba(16,38,100,0.08);
        }
        &:active {
            background: $color-secondary-dark;
            color: #fff;
        }
        &:disabled, &.disabled {
            background: $gray-200;
            color: $gray-400;
            cursor: not-allowed;
        }
    }
    &.big {
        font-size: $font-size-lg;
        line-height: 1.5;
        padding: $spacing-lg $spacing-xl;
    }
    &.large {
        font-size: 22px;
        line-height: 1.5;
        padding: $spacing-lg $spacing-xl;
    }
    &.small {
        font-size: $font-size-sm;
        padding: $spacing-sm $spacing-md;
    }
    &.link {
        background: none;
        color: $color-link-default;
        font-weight: $font-weight-regular;
        padding: 0;
        text-decoration: underline;
        box-shadow: none;
        &:hover, &:focus {
            color: $color-link-hover !important;
            text-decoration: underline;
        }
        &.semibold {
            font-weight: $font-weight-semibold;
        }
        &.bold {
            font-weight: $font-weight-bold;
        }
        &.dark {
            color: $color-text;
        }
    }
    &.wide {
        width: 100%;
    }
}
