import React, { useState } from "react";
import Modal from '../../../app/shared/Modal.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Form } from 'antd';
import Button from '../../../app/shared/Button.tsx';
import FileUpload from '../../../app/shared/form/FileUpload.tsx';
import {ReactComponent as IconUpload} from '../../../../assets/icons/icon-upload-cloud.svg';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onSuccess: (file: File) => void;
}

const UploadPdfModal: React.FC<Props> = ({show, onClose, onSuccess}) => {
    const [form] = Form.useForm();
    const file = Form.useWatch('file', form);
    const {currentClaim} = useClaimsContext();
    const [canSubmit, setCanSubmit] = useState<boolean>(false);
    
    const onSubmit = async (fields: any) => {
        setCanSubmit(false);
        if (!currentClaim?.id || !fields.file.length) {
            return;
        }
        onSuccess(fields.file[0].originFileObj);
    }
    
    return (
        <Modal
            show={show}
            onClose={onClose}
            title="Create your estimate"
            description="Upload your original estimate below."
        >
            <div className="font-weight-bold margin-bottom-16">Upload your original estimate</div>
            
            <CustomForm
                form={form}
                onSubmit={onSubmit}
            >
                <FileUpload
                    showProgressBar={true}
                    name="file"
                    rules={[{ required: true, message: 'Please upload a file!' }]}
                    showUploadList={false}
                    onFinish={(value) => setCanSubmit(value)}
                    onCustomRemove={() => setCanSubmit(false)}
                    maxFileSize={10}
                >
                    <div className="claim-ce-upload" aria-label="File upload area">
                        <IconUpload className="claim-ce-upload-icon" aria-label="Upload icon" />
                        <div className="claim-ce-upload-description">
                            <div className="claim-ce-upload-description-title">Select a file or drag and drop here</div>
                            <div className="claim-ce-upload-description-label">(JPG, PNG or PDF, file size no more than 10MB)</div>
                        </div>
                        <Button size="large" htmlType="button">Select File</Button>
                    </div>
                </FileUpload>
                <div className="claim-ce-modal-footer">
                    <Button
                        color="primary"
                        variant="solid"
                        wide={true}
                        disabled={!file || !canSubmit}
                        aria-label="Next"
                    >
                        Next
                    </Button>
                </div>
            </CustomForm>
        </Modal>
    );
};

export default UploadPdfModal;
