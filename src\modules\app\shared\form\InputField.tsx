import React from 'react';
import { Form, FormItemProps, Input, InputProps } from 'antd';
import classNames from 'classnames';

interface Props extends FormItemProps {
    className?: string;
    disabled?: boolean;
    fontSize?: 'default' | 'medium';
    inputProps?: InputProps,
    label?: string;
    name?: string | (string | number)[];
    placeholder?: string;
    rules?: any[];
    fieldSize?: 'default' | 'medium';
    type?: string;
}

const InputField: React.FC<Props> = ({
    className,
    disabled,
    fontSize = 'regular',
    inputProps,
    label,
    name,
    placeholder,
    rules,
    fieldSize,
    type = "text",
    ...props
}) => {
    return (
        <Form.Item
            className={classNames("form-field", fontSize, className)}
            name={name}
            label={label}
            rules={rules}
            {...props}
        >
            <Input disabled={disabled} className={classNames("form-field-input", fieldSize)} type={type} placeholder={placeholder} {...inputProps}/>
        </Form.Item>
    );
};

export default InputField;
