import React from 'react';
import Modal from './Modal.tsx';
import Button, { ButtonSize } from './Button.tsx';

interface Props {
    children?: React.ReactNode;
    className?: string;
    description?: React.ReactNode;
    onClose: VoidFunction;
    onSuccess: VoidFunction;
    show: boolean;
    title?: string;
}

const Dialog: React.FC<Props> = ({
    children,
    className,
    description,
    onClose,
    onSuccess,
    show,
    title = 'Are you sure?',
}) => {
    return (
        <Modal
            className={className}
            disableCloseIcon={true}
            show={show}
            position="center"
            title={title}
            description={description}
            onClose={onClose}
        >
            {children && <div className="margin-top-24">{children}</div>}

            <div className="modal-content-actions" style={{ textAlign: 'center' }}>
                <Button
                    className="submit"
                    color="primary"
                    variant="solid"
                    onClick={() => onSuccess()}
                    buttonSize={ButtonSize.REGULAR}
                    style={{
                        marginRight: '10px',
                    }}
                >
                    Yes
                </Button>
                <Button
                    color="primary"
                    variant="outlined"
                    onClick={() => onClose()}
                    buttonSize={ButtonSize.REGULAR}
                >
                    No
                </Button>
            </div>
        </Modal>
    );
};

export default Dialog;
