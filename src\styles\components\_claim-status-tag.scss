// status tag
.cst {
    @include flex(inline-flex, row, wrap, flex-start, center);
    background-color: #EFEFEF;
    border-radius: 40px;
    font-size: 12px;
    font-weight: $font-weight-bold;
    gap: 8px;
    padding: 4px 8px;
    text-transform: capitalize;

    .icon {
        font-size: 8px;
    }

    &.new {
        background-color: rgba($color-status-new, .1);
        color: $color-status-new;
    }

    &.pending {
        background-color: rgba($color-status-pending, .1);
        color: $color-status-pending;
    }

    &.scheduling {
        background-color: rgba($color-status-scheduling, .1);
        color: $color-status-scheduling;
    }

    &.storage {
        background-color: rgba($color-status-storage, .1);
        color: $color-status-storage;
    }

    &.closed {
        background-color: rgba($gray-400, .1);
        color: $gray-400;
    }
}
